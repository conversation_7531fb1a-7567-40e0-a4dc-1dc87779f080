import {
  Box,
  Checkbox,
  Chip,
  CircularProgress,
  Grid,
  IconButton,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { bgColors } from "../../utils/bgColors";
import SearchIconSvg2 from "../../assets/svgs/SearchIconSvg2";
import { IoPersonAddOutline } from "react-icons/io5";
import FilterIconSvg from "../../assets/svgs/FilterIconSvg";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import ReadIconSvg from "../../assets/svgs/ReadIconSvg";
import EventIcon from "@mui/icons-material/Event";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import ChatFilterMainPopover from "./chatSideBarComponents/ChatFilterMainPopover";
import NewContactPopOver from "../ContactsComponents/NewContactPopOver";
import DateColumnRangeFilterPopover, {
  DateColumnRange,
  DateRange,
} from "../common/DateColumnRangeFilterPopover";
import { useAppSelector } from "../../utils/redux-hooks";
import TimeDifference from "./inboxDetailsComponents/LastMessageTimeDisplay";
import ChatArrowDownSvg from "../../assets/svgs/ChatArrowDownSvg";
import AssignToPopover from "./inboxDetailsComponents/AssignToPopover";
import AssignmentIndIcon from "@mui/icons-material/AssignmentInd";
import BasicFilterPopover, {
  basicFilterMapping,
  getSelectedFilter,
} from "./chatSideBarComponents/BasicFilterPopOver";
import ReportGmailerrorredIcon from "@mui/icons-material/ReportGmailerrorred";
import LoadingComponent from "../common/LoadingComponent";
import { formatLastMessage } from "../../utils/functions";
import { ContactData, LoadingStates } from "../../pages/home/<USER>";
import AccountMetricsBar from "./inboxDetailsComponents/AccountMetricsBar";

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white1,
    height: "100vh",
    width: "100%",
  },
  searchField: {
    // width: "100%",
    borderRadius: "12px",
    height: "38px",
    // fontSize: "14px",
    backgroundColor: "transparent",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "Inter",
      color: "#4B5A5A !important",
      fontWeight: "500 !important",
    },
  },
  grayColor: {
    color: "#4B5A5A !important",
    opacity: "60%",
    // padding:"5px"
  },
  spaceBetween: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    mt: 1,
    justifyContent: "space-between",
  },
  messageIconStyles: {
    // backgroundColor: "#00934F",
    border: "1px solid #00934F",
    borderRadius: "8px",
    width: "43px",
    height: "30px",
    // padding: "8px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    cursor: "pointer",
  },
  filterContainer: {
    display: "flex",
    flexDirection: "row",
  },
  loadMoreContainer: {
    fontSize: 12,
    marginInline: "auto",
    // maxWidth: "160px",
    textAlign: "center",
    backgroundColor: bgColors?.gray4,
    // borderRadius: "14px",
    padding: "4px",
    // marginBottom: "8px",
    cursor: "pointer",
    "&:hover": {
      color: bgColors?.blue,
    },
  },
  borderContainer: {
    border: "2px solid #F2F2F2",
    borderRadius: "8px",
    height: "32px",
    padding: "4px",
    alignItems: "center",
    cursor: "pointer",
    "&:hover": {
      border: `2px solid ${bgColors.green1}`,
      cursor: "pointer",
      backgroundColor: "rgba(68, 71, 70, 0.08)",
    },
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    fontWeight: "600 !important",
    overflow: "hidden",
    whiteSpace: "nowrap",
    maxWidth: "120px",
    textOverflow: "ellipsis",
  },
  greenButton: {
    backgroundColor: bgColors.green1,
    color: bgColors.green,
    fontWeight: "700",
    borderRadius: 12,
    paddingInline: "6px",
  },
  yellowButton: {
    backgroundColor: bgColors.yellow2,
    color: bgColors.yellow,
    fontWeight: "700",
    borderRadius: 12,
    paddingInline: "6px",
  },
  redButton: {
    backgroundColor: bgColors.red2,
    color: bgColors.red1,
    fontWeight: "700",
    borderRadius: 12,
    paddingInline: "6px",
  },
  darkGreenButton: {
    backgroundColor: bgColors.green,
    color: bgColors.white,
    fontWeight: "700",
    borderRadius: 12,
    paddingInline: "6px",
  },
  chatDetailsContainer: {
    display: "flex",
    flexDirection: "row",
    cursor: "pointer",
    width: "100%",
    paddingInline: "6px",
    borderRadius: "6px",
    alignItems: "center",
  },
  contactsCount: {
    backgroundColor: bgColors?.gray5,
    color: bgColors?.black,
    borderRadius: "24px",
    height: "24px",
    width: "34px",
    fontSize: "10px",
    textAlign: "center",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  basicFilterButton: {
    color: bgColors.white,
    border: "2px solid #F2F2F2",
    borderRadius: "12px",
    fontSize: "12px",
    // width: "140px",
    padding: "10px 5px",
    display: "flex",
    height: "32px",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  helpContainer: {
    display: "flex",
    flexDirection: "row",
    cursor: "pointer",
  },
  helpTextContainer: {
    display: "flex",
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  flexRowContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  notificationContainer: {
    backgroundColor: bgColors.green,
    color: bgColors.white,
    padding: "2px 3px 1px 3px",
    width: "16px",
    borderRadius: "50%",
    textAlign: "center",
  },
  chatDetailsContainerHover: {
    boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.01)",
    transition: "box-shadow 0.3s ease",
    cursor: "pointer",
    width: "100%",
    "&:hover": {
      boxShadow: "0px 6px 15px rgba(0, 0, 0, 0.3)",
    },
  },
  chatClicked: {
    backgroundColor: "#D9D9D9",
  },
});

const ChatSideBar = ({
  contacts,
  setContacts,
  searchInput,
  filterData,
  chatsPageNumber,
  contactsListLoading,
  setContactsListLoading,
  setSearchInput,
  setFilterData,
  setContactNumber,
  setChatsPageNumber,
  handleCloseDrawer,
  setTotalUnreads,
  dateRangeFilter,
  setDateRangeFilter,
  refreshContactsList,
}: {
  contacts: any;
  setContacts: any;
  searchInput: string;
  filterData: any;
  chatsPageNumber: number;
  contactsListLoading: LoadingStates;
  setSearchInput: (search: any) => void;
  setFilterData: (filter: any) => void;
  setContactNumber: (number: any) => void;
  setChatsPageNumber: (number: any) => void;
  handleCloseDrawer?: () => void;
  setContactsListLoading: (loading: LoadingStates) => void;
  setTotalUnreads: (unreads: any) => void;
  dateRangeFilter?: DateColumnRange;
  setDateRangeFilter?: (filter: DateColumnRange) => void;
  refreshContactsList?: () => void;
}) => {
  const classes = useStyles();
  const navigate = useNavigate();
  const params = useParams();

  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const latestInboxContactsSlice = useAppSelector(
    (state: any) => state.latestInboxContacts
  );

  const manageInboxObject = getuserPermissionData?.inbox;
  const contactsData = contacts?.data;

  const accountData = useAppSelector((state: any) => state?.accountData?.data);
  const [anchorEl, setAnchorEl] = useState(null);
  const [openBasicFilter, setOpenBasicFilter] = useState<any>(null);
  const [selectedFilterText, setSelectedFilterText] = useState<string>("");
  const [dateFilterAnchorEl, setDateFilterAnchorEl] =
    useState<HTMLElement | null>(null);

  const [selectedItems, setSelectedItems] = useState<any>([]);
  const [activateChatSelection, setActivateChatSelection] =
    useState<boolean>(false);
  const [anchorElement, setAnchorElement] = useState(null);
  const [openNewContactPopover, setOpenNewContactPopover] = useState(false);
  const [addContactTooltip, setAddContactTooltip] = useState(false);

  const areFiltersApplied = () => {
    const hasFilters = filterData.filters.length > 0;
    return hasFilters;
  };

  const hasAddContactPermission = (permission: any) => {
    for (const profileItem of permission) {
      if (
        Object.prototype.hasOwnProperty.call(profileItem, "addNewInboxContact")
      ) {
        return true;
      }
    }
    return false;
  };

  const handleMainFilterClick = (event: any) => {
    setAnchorElement(event.currentTarget);
  };

  const handleCloseMainPopover = () => {
    setAnchorElement(null);
  };

  const handleDateFilterOpen = (event: React.MouseEvent<HTMLElement>) => {
    setDateFilterAnchorEl(event.currentTarget);
  };

  const handleDateFilterClose = () => {
    setDateFilterAnchorEl(null);
  };

  const handleDateRangeApply = (dateRange: DateColumnRange) => {
    if (setDateRangeFilter) {
      setDateRangeFilter(dateRange);
    }
    handleDateFilterClose();
    // Call API after applying date filter
    if (refreshContactsList) {
      refreshContactsList();
    }
  };

  const handleDateRangeClear = () => {
    if (setDateRangeFilter) {
      setDateRangeFilter({
        startDate: "",
        endDate: "",
        column: "CreatedDate",
      });
    }
    // Call API after clearing date filter
    if (refreshContactsList) {
      refreshContactsList();
    }
  };

  const handleOpenAssignPopup = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseAssignPopup = () => {
    setAnchorEl(null);
  };

  const handleSelectAll = () => {
    if (selectedItems?.length === contactsData?.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(contactsData);
    }
  };

  const handleSelection = () => {
    if (selectedItems?.length !== 0) {
      setSelectedItems([]);
      setActivateChatSelection(false);
    } else {
      setActivateChatSelection(!activateChatSelection);
    }
  };

  const handleSearchChange = (event: any) => {
    const inputValue = event.target.value;

    setSearchInput(inputValue);
  };

  const handleItemClick = (checkedContact: any) => {
    // Check if the checkedContact is already selected
    const isSelected = selectedItems?.some(
      (selectedItem: any) =>
        selectedItem?.contactId === checkedContact?.contactId
    );

    if (isSelected) {
      // If checkedContact is already selected, remove it from the selected items
      setSelectedItems(
        selectedItems?.filter(
          (selectedItem: any) =>
            selectedItem?.contactId !== checkedContact?.contactId
        )
      );
    } else {
      // If checkedContact is not selected, add it to the selected items
      setSelectedItems([...selectedItems, checkedContact]);
    }
  };

  const handleApplyFilter = (filterData: any) => {
    setFilterData(filterData);
    navigate("/inbox/help");
    // Call API after applying filter
    if (refreshContactsList) {
      refreshContactsList();
    }
  };
  const handleApplyBasicFilter = (filterData: any) => {
    setSelectedFilterText(filterData?.filterText);
    setFilterData(filterData?.filters);
    navigate("/inbox/help");
    // Call API after applying basic filter
    if (refreshContactsList) {
      refreshContactsList();
    }
  };

  const handleOpenNewContactPopup = () => {
    const hasPermission = hasAddContactPermission(manageInboxObject);
    if (hasPermission) {
      setOpenNewContactPopover(true);
    } else {
      setAddContactTooltip(true);
      setTimeout(() => {
        setAddContactTooltip(false);
      }, 2000);
    }
  };

  const handleCloseNewContactPopup = () => {
    setOpenNewContactPopover(false);
  };

  useEffect(() => {
    const selected = getSelectedFilter(filterData?.filters, basicFilterMapping);
    setSelectedFilterText(selected);
  }, [filterData]);

  const loadMoreContacts = () => {
    if (
      !contactsListLoading?.paginatedContactsList &&
      contacts?.data?.length !== contacts?.totalCount &&
      chatsPageNumber <= Math.ceil(contacts?.totalCount / 30)
    ) {
      setContactsListLoading({
        allContactsList: false,
        paginatedContactsList: true,
      });
      setChatsPageNumber(chatsPageNumber + 1);
    }
  };

  useEffect(() => {
    if (
      searchInput ||
      filterData ||
      (dateRangeFilter?.startDate && dateRangeFilter?.endDate)
    ) {
      setChatsPageNumber(1);
    }
  }, [searchInput, filterData, dateRangeFilter]);

  return (
    <Grid className={classes?.mainContainer}>
      <Box width={"100%"}>
        <Box sx={{ display: { xs: "none", md: "block" } }}>
          <AccountMetricsBar />
        </Box>
        <Box p={1} className={classes?.spaceBetween}>
          <TextField
            className={classes?.searchField}
            variant="standard"
            size="small"
            fullWidth
            value={searchInput}
            // disabled={accountData?.companyVerificationStatus === false}
            InputProps={{
              disableUnderline: true,
              style: {
                padding: "10px",
                fontSize: "12px",
                height: "38px",
                fontWeight: "600 !important",
                border: `1px solid ${bgColors.gray3}`,
                borderRadius: "5px",
              },
              startAdornment: (
                <IconButton sx={{ p: 0, paddingTop: "3px" }}>
                  <SearchIconSvg2 />
                </IconButton>
              ),
            }}
            inputProps={{
              style: {
                // Additional style for placeholder
                fontWeight: "600 !important",
                paddingTop: "3px", // Apply font weight here
              },
            }}
            onChange={handleSearchChange}
            placeholder="Search chats"
          />

          <Tooltip
            title="Access Denied"
            placement="top"
            open={addContactTooltip}
            onClose={() => setAddContactTooltip(false)}
          >
            <Box
              ml={1}
              className={classes?.messageIconStyles}
              onClick={handleOpenNewContactPopup}
              sx={{
                "&:hover": {
                  cursor: "pointer",
                  backgroundColor: "rgba(68, 71, 70, 0.08)",
                },
              }}
            >
              <IoPersonAddOutline
                style={{
                  stroke: bgColors.green,
                  // display: "block",
                }}
              />
            </Box>
          </Tooltip>
        </Box>
        <Box
          className={classes?.spaceBetween}
          px={1}
          pb={1}
          sx={{
            backgroundColor: `${bgColors.gray9}`,
          }}
        >
          {
            // selectedItems?.length !== 0 && selectedItems !== undefined
            activateChatSelection === true ? (
              <Box display="flex" alignItems="center" sx={{ mt: 1 }}>
                <Checkbox
                  checked={selectedItems?.length === contactsData?.length}
                  onChange={handleSelectAll}
                  color="default"
                  size="small"
                  sx={{
                    width: 36,
                    height: 30,
                  }}
                />
                <Typography
                  sx={{
                    textAlign: "center",
                    color: bgColors?.black,
                    fontSize: "14px",
                  }}
                >
                  &nbsp;&nbsp;{selectedItems?.length}&nbsp;Selected
                </Typography>
              </Box>
            ) : (
              <Box className={classes?.basicFilterButton} sx={{ mt: 1 }}>
                <Box className={classes?.contactsCount}>
                  {contactsListLoading?.allContactsList &&
                  !contactsListLoading?.paginatedContactsList ? (
                    <CircularProgress size={15} />
                  ) : (
                    <span>
                      {contacts?.totalCount > 9999
                        ? (contacts.totalCount / 1000)
                            .toFixed(1)
                            .replace(/\.0$/, "") + "k"
                        : contacts?.totalCount}
                    </span>
                  )}
                  {}
                </Box>
                <Box
                  pl={0.5}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    cursor: "pointer",
                    justifyContent: "space-between",
                  }}
                  onClick={(e) => setOpenBasicFilter(e.currentTarget)}
                >
                  <Typography
                    sx={{
                      textAlign: "center",
                      color: bgColors?.gray1,
                      fontSize: "10px",
                      px: 0.5,
                    }}
                  >
                    {selectedFilterText}&nbsp;
                  </Typography>
                  <ChatArrowDownSvg
                    style={{
                      width: "15px",
                      height: "15px",
                      stroke: bgColors.green,
                    }}
                  />
                </Box>
              </Box>
            )
          }
          <Box gap={2} className={classes?.filterContainer}>
            {
              // selectedItems?.length !== 0 && selectedItems !== undefined
              activateChatSelection === true ? (
                <Tooltip title="Assign">
                  <Box
                    className={classes?.borderContainer}
                    onClick={handleOpenAssignPopup}
                    sx={{ mt: 1 }}
                  >
                    <AssignmentIndIcon fontSize="small" />
                  </Box>
                </Tooltip>
              ) : (
                <>
                  <Tooltip title="Date Filter">
                    <Box
                      className={classes?.borderContainer}
                      sx={{
                        mt: 1,
                        backgroundColor:
                          dateRangeFilter?.startDate && dateRangeFilter?.endDate
                            ? bgColors.green
                            : "transparent",
                        "& .MuiSvgIcon-root": {
                          color:
                            dateRangeFilter?.startDate &&
                            dateRangeFilter?.endDate
                              ? "#fff"
                              : "inherit",
                        },
                      }}
                      onClick={handleDateFilterOpen}
                    >
                      <EventIcon fontSize="small" />
                    </Box>
                  </Tooltip>
                  <Tooltip title="Apply Filters">
                    <Box
                      className={classes?.borderContainer}
                      sx={{ mt: 1 }}
                      onClick={handleMainFilterClick}
                    >
                      {areFiltersApplied() ? (
                        <FilterAltIcon />
                      ) : (
                        <FilterIconSvg />
                      )}
                    </Box>
                  </Tooltip>
                </>
              )
            }
            <Tooltip
              title={
                // selectedItems?.length === 0 || selectedItems === undefined
                activateChatSelection === false
                  ? "Click to select chats"
                  : "Clear selection"
              }
            >
              <Box
                className={classes?.borderContainer}
                sx={{
                  backgroundColor: activateChatSelection
                    ? "rgba(68, 71, 70, 0.08)"
                    : "transparent",
                  mt: 1,
                }}
                onClick={handleSelection}
              >
                <ReadIconSvg />
              </Box>
            </Tooltip>
          </Box>
        </Box>
        {accountData?.companyVerificationStatus === false && (
          <Box sx={{ textAlign: "center", m: 2 }}>
            <Chip
              label={
                <Typography sx={{ fontSize: 10, textAlign: "center" }}>
                  Your account has not been verified with meta.
                  <br />
                  Please link with meta to chat with your contacts.
                </Typography>
              }
              color="warning"
              size="small"
              sx={{ height: "auto", width: "100%", padding: "4px" }}
            />
          </Box>
        )}
      </Box>
      <Box
        sx={{
          height: {
            xs: "calc(100vh - 180px)",
            sm: "calc(100vh - 215px)",
            md: "calc(100vh - 232px)",
          },
          overflowY: "auto",
          overflowX: "hidden",
        }}
      >
        {contactsListLoading?.allContactsList &&
        !contactsListLoading?.paginatedContactsList ? (
          <LoadingComponent color={bgColors?.blue} height="auto" />
        ) : (
          <>
            {contactsData?.length !== 0 ? (
              contactsData?.map((chat: any, index: number) => (
                <Box
                  // onScroll={handleScroll}
                  // ref={tableContainerRef}
                  className={`${classes?.chatDetailsContainer} ${
                    classes?.chatDetailsContainerHover
                  } ${
                    params?.id === chat?.contact ? classes?.chatClicked : ""
                  }`}
                  key={index}
                  onClick={() => {
                    if (activateChatSelection === false) {
                      navigate(`/inbox/${chat?.contact}`);
                      // setSearchInput("");
                      setContactNumber(chat?.contact);
                      setContacts((prevContacts: ContactData) => ({
                        ...prevContacts,
                        data: prevContacts.data.map((contact: any) =>
                          contact.contact === chat?.contact
                            ? { ...contact, unRead: 0 }
                            : contact
                        ),
                      }));
                      setTotalUnreads((prev: any) => prev - chat?.unRead);
                      if (handleCloseDrawer) {
                        handleCloseDrawer();
                      }
                    } else {
                      handleItemClick(chat);
                    }
                  }}
                >
                  <Box display="flex" alignItems="center" py={0.5}>
                    {activateChatSelection ? (
                      <Checkbox
                        checked={
                          selectedItems
                            ?.map((item: any) => item?.contactId)
                            ?.includes(chat?.contactId) || false
                        }
                        onChange={() => handleItemClick(chat)}
                        color="default"
                        size="small"
                        sx={{ width: 40, height: 40 }}
                      />
                    ) : chat?.isSpam ? (
                      <Box
                        sx={{
                          width: 36,
                          height: 36,
                          margin: 0.5,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          borderRadius: "50%",
                          overflow: "hidden",
                          border: "1px solid #ccc",
                          background: bgColors?.red2,
                        }}
                      >
                        <ReportGmailerrorredIcon
                          sx={{ color: bgColors?.red }}
                          fontSize="medium"
                        />
                      </Box>
                    ) : (
                      <img
                        src={
                          selectedItems
                            ?.map((item: any) => item?.contactId)
                            ?.includes(chat?.contactId)
                            ? "/images/TICK.png"
                            : "/images/profile.png"
                        }
                        height={40}
                        alt="chartIcon1"
                      />
                    )}
                  </Box>
                  <Box
                    px={1}
                    py={chat?.chatStatus === "resolved" ? 1 : 0.5}
                    ml="auto"
                    sx={{ width: "100%" }}
                  >
                    <Box className={classes?.helpTextContainer}>
                      <Box className={classes?.flexRowContainer}>
                        <Typography
                          sx={{ fontSize: { xs: "10px", md: "12px" } }}
                          className={classes?.blackColor}
                        >
                          {chat?.name || chat?.contact}
                        </Typography>
                        <Box
                          ml={1}
                          className={
                            chat?.chatStatus === "open"
                              ? classes?.greenButton
                              : chat?.chatStatus === "expired"
                              ? classes?.yellowButton
                              : chat?.chatStatus === "new"
                              ? classes?.darkGreenButton
                              : classes?.redButton
                          }
                        >
                          <Typography
                            sx={{
                              fontSize: "10px",
                              textTransform: "capitalize",
                            }}
                            alignItems="center"
                          >
                            {chat?.chatStatus}
                          </Typography>
                        </Box>
                      </Box>
                      <Typography
                        sx={{ fontSize: "9px" }}
                        className={classes?.grayColor}
                        key={index}
                      >
                        {chat?.lastMessageAt !== null && (
                          <TimeDifference lastMessageAt={chat?.lastMessageAt} />
                        )}
                      </Typography>
                    </Box>
                    <Box className={classes?.flexRowContainer}>
                      <Typography
                        sx={{ fontSize: { xs: "9px", md: "11px" } }}
                        className={classes?.grayColor}
                        dangerouslySetInnerHTML={{
                          __html: formatLastMessage(
                            chat?.lastMessage?.length > 40
                              ? `${chat?.lastMessage?.slice(0, 40)}...`
                              : chat?.lastMessage
                          ),
                        }}
                      />

                      {chat?.unRead !== 0 && (
                        <Typography
                          sx={{ fontSize: "9px", ml: "auto" }}
                          className={classes?.notificationContainer}
                        >
                          {chat?.unRead}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </Box>
              ))
            ) : (
              <Box sx={{ textAlign: "center", mt: 2 }}>No chats found</Box>
            )}
          </>
        )}

        {contactsData?.length !== 0 &&
          chatsPageNumber < contacts?.totalPages &&
          !contactsListLoading?.allContactsList && (
            <Box
              onClick={
                !contactsListLoading?.paginatedContactsList
                  ? loadMoreContacts
                  : undefined
              }
              className={classes.loadMoreContainer}
            >
              {!contactsListLoading?.allContactsList &&
              contactsListLoading?.paginatedContactsList ? (
                <LoadingComponent height="20px" color={bgColors.blue} />
              ) : (
                "Click to view more chats..."
              )}
            </Box>
          )}
      </Box>

      <AssignToPopover
        contact={selectedItems}
        anchorEl={anchorEl}
        onClose={handleCloseAssignPopup}
      />
      <NewContactPopOver
        open={openNewContactPopover}
        handleClose={handleCloseNewContactPopup}
      />
      <ChatFilterMainPopover
        anchorEl={anchorElement}
        handleClose={handleCloseMainPopover}
        onApplyFilter={handleApplyFilter}
        filterData={filterData}
        areFiltersApplied={areFiltersApplied}
      />
      <BasicFilterPopover
        anchorEl={openBasicFilter}
        handleClose={() => setOpenBasicFilter(null)}
        handleSelectBasicFilter={handleApplyBasicFilter}
        selectedFilterText={selectedFilterText}
      />
      <DateColumnRangeFilterPopover
        anchorEl={dateFilterAnchorEl}
        handleClose={handleDateFilterClose}
        onApplyFilter={handleDateRangeApply}
        onClearFilter={handleDateRangeClear}
        initialDateRange={
          dateRangeFilter || {
            startDate: "",
            endDate: "",
            column: "CreatedDate",
          }
        }
        title="Filter Chats by Date"
        columnOptions={[
          { value: "CreatedDate", label: "Created Date" },
          { value: "LastMessageAt", label: "Last Message At" },
        ]}
      />
    </Grid>
  );
};

export default ChatSideBar;
