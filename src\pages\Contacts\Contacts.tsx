import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Checkbox,
  Chip,
  Grid,
  IconButton,
  <PERSON>over,
  Typography,
} from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import { bgColors, getStylesByValue } from "../../utils/bgColors";
import { makeStyles } from "@mui/styles";
import NewContactPopOver from "../../components/ContactsComponents/NewContactPopOver";
import EditPopOver from "../../components/ContactsComponents/EditPopOver";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { fetchAllContacts } from "../../redux/slices/Contacts/AllContactsSlice";
import { fetchAllRestoredContacts } from "../../redux/slices/Contacts/RestoreContactsSlice";
import { CONTACTS_APIS } from "../../Apis/Contacts/ContactsApis";
import { CONTACT_TAGS_APIS } from "../../Apis/Contacts/ContactTagsApis";
import { toast } from "react-toastify";
import ImportContactsPopUp from "../../components/ContactsComponents/ImportContactsPopUp";
import ContactsTracterPop from "../../components/ContactsComponents/contactsTrackerPopUp";
import { toastActions } from "../../utils/toastSlice";
import DeletePopUp from "../../components/common/DeletePopup";
import { LuImport } from "react-icons/lu";
import { CgExport } from "react-icons/cg";
import TrackChangesIcon from "@mui/icons-material/TrackChanges";
import { MdDriveFileMoveOutline } from "react-icons/md";
import { removeContact } from "../../redux/slices/Contacts/DeleteContactSlice";
import NoAccessPage from "../../components/common/NoAccess";
import { fetchAllDeletedContacts } from "../../redux/slices/Contacts/GetDeletedContacts";
import RestoreIconSvg from "../../assets/svgs/RestoreIconSvg";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Sync as SyncIcon,
} from "@mui/icons-material";
import CommonTable from "../../components/common/CommonTable";
import {
  mainFilterOptions,
  statusOptions,
  chatStatusOptions,
  sourceOptions,
} from "../../utils/constants";
import CustomMainFilterPopover, {
  SelectedFilter,
} from "../../components/common/CustomMainFilerPopover";
import { DateRange } from "../../components/common/DateRangeFilterPopover";
import RestorePopUp from "../../components/common/RestorePopup";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import RedCloseIcon from "../../assets/svgs/RedCloseIcon";
import { getContactTags } from "../../redux/slices/Contacts/getContactTags";
import ContactsFilterPopover from "../../components/ContactsComponents/ContactsFilterPopOver";
import { debounce } from "lodash";
import MoveToTagsPopOver from "../../components/ContactsComponents/MoveToTagsPopOver";
import DateRangeIcon from "@mui/icons-material/DateRange";
import SyncContactsBySourcesPopover from "../../components/ContactsComponents/SyncContactsBySourcesPopover";
import moment from "moment";

const useStyles = makeStyles({
  bgContainer: {
    backgroundColor: bgColors.white,

    overflow: "100%",
    // height:"100vh !important",
    height: "calc(100vh - 3px)",
    width: "100%",
    // overflowY:"hidden",
    // scrollbarWidth:"none"
  },
  manageTeamContainer: {
    display: "flex",

    width: "full",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    // fontWeight: "600 !important",
  },
  searchField: {
    width: "100%",
    borderRadius: "12px",
    // height: "38px",
    // backgroundColor: bgColors.white2,
    backgroundColor: "white",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "inter",
      color: "#000000 !important",
    },
  },
  SaveChangesButton: {
    // backgroundColor: bgColors.green,
    // color: bgColors.white,
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    // width: "fit-content",
    width: "140px",
    height: "32px",
    // padding: "10px",
    cursor: "pointer",
  },
  messageCount: {
    backgroundColor: bgColors?.gray5,
    color: bgColors?.black,
    borderRadius: "24px",
    height: "24px",
    width: "34px",
    fontSize: "10px",
    textAlign: "center",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  tagsCount: {
    backgroundColor: "#F4F4F4",
    borderRadius: "24px",
    padding: "3px",
    height: "25px",
    width: "150px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
    whiteSpace: "nowrap",
    textOverflow: "ellipsis",
    cursor: "pointer",
  },
  messageCountContainer: {
    border: "1px solid #F2F2F2",
    cursor: "pointer",
    borderRadius: "5px",
    paddingInline: "8px",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  MuiLoadingButtonRoot: {
    backgroundColor: "#FFF",
  },
  campaignCountContainer: {
    // border: `1px solid ${bgColors.green}`,
    cursor: "pointer",
    color: bgColors.black,
    borderRadius: "8px",
    paddingInline: "8px",
    display: "flex",
    flexDirection: "row",
    height: "34px",
    alignItems: "center",
  },
  campaignCountContainer1: {
    border: "1px solid #F2F2F2",
    color: bgColors.green,
    borderRadius: "20px",
    paddingInline: "8px",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  messageInnerContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  grayColor: {
    color: "#303030",
    opacity: "60%",
    fontSize: "13px",
    // padding:"5px"
  },
  grayColor1: {
    color: "#303030",
    opacity: "90%",
    fontSize: "13px",
    // padding:"5px"
  },
  campaignColor: {
    color: bgColors.green,
    // color: "#007aff",
    fontSize: "13px",
    fontWeight: "Semi Bold",
    // padding:"5px"
  },
  iconStyles: {
    cursor: "pointer",
    paddingLeft: "5px",
    alignItems: "center",
    // marginTop: "2px",
    color: "#ffffff",
  },
  rotatedIcon: {
    cursor: "pointer",
    paddingRight: "5px",
    transform: "rotate(180deg)",
  },
  table: {
    borderCollapse: "separate",
    borderSpacing: "0",
    textAlign: "center",
    borderColor: "lightgray",
    "& th, & td": {
      // borderTop: '1px solid gray',
      borderBottom: "1px solid #f0f0f0",
    },
    "& th:first-child, & td:first-child": {
      borderLeft: "none",
    },
    "& th:last-child, & td:last-child": {
      borderRight: "none",
    },
  },
  teamProfileContainer: {
    display: "flex",
    alignItems: "center",
  },
  editButtonContainer: {
    border: "2px solid #DBDBDB",
    padding: "8px",
    borderRadius: "12px",
    backgroundColor: "#F4F4F4",
    width: "50px",
    // paddingBottom: 0,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    cursor: "pointer",
  },
});

const Contacts = () => {
  const classes = useStyles();
  const dispatch = useAppDispatch();

  //states for logged in user data
  const userProfileSlice = useAppSelector((state: any) => state?.adminLogin);
  const userData = userProfileSlice?.data;

  //states for contacts data
  const contactsSlice = useAppSelector((state: any) => state?.contactsData);
  const contactsData = contactsSlice?.data;
  const contactsLoading = contactsSlice?.status === "loading";

  //states for deleted contacts data
  const deletedContactsSlice = useAppSelector(
    (state: any) => state.deletedContactsData
  );
  const deletedContactsData = deletedContactsSlice?.data;
  const allDeletedContactsData = deletedContactsData?.data;
  const deletedContactsLoading = deletedContactsSlice?.status === "loading";

  //states for permissions
  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const manageContactsObject = getuserPermissionData?.contacts;

  //states for wallet
  const currentPlanDetails = useAppSelector(
    (state: any) => state?.wallet?.walletAndSubscription?.data
  );

  //states for account
  const accountData = useAppSelector((state: any) => state?.accountData?.data);

  //states for contact tags
  const getContactTagsData = useAppSelector(
    (state: any) => state?.getContactTagsData?.data
  );

  const [anchorEl, setAnchorEl] = useState(null);
  const [anchorElContact, setAnchorElContact] = useState(null);
  const [editContact, setEditContact] = useState(null);
  const [anchorelMove, setAnchorElMove] = useState(null);
  const [selectedFilter, setSelectedFilter] = useState("Tags");

  const [selectedContactFilter, setSelectedContactFilter] =
    useState<SelectedFilter>({
      id: "1",
      value: "View All",
    });
  console.log("selectedContactFilter", selectedContactFilter);

  const [openDialog, setOpenDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [dialog, setDialog] = useState(false);
  const [trackerDialog, setTrackerDialog] = useState(false);
  const [getId, setGetId] = useState(null);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [restoreDialog, setRestoreDialog] = useState(false);
  const [deleteSelectedDialog, setDeleteSelectedDialog] = useState(false);
  const [restoreSelectedDialog, setRestoreSelectedDialog] = useState(false);
  const [teamData, setTeamData] = useState<any>([]);
  const [deletedContacts, setDeletedContacts] = useState<any>([]);
  const [perPage, setPerPage] = useState(10);
  const [page, setPage] = useState(1);
  const [searchContactsQuery, setSearchContactsQuery] = useState<string>("");
  const [addContactTooltip, setAddContactTooltip] = useState(false);
  const [selectAllChecked, setSelectAllChecked] = useState<boolean>(false);
  const [importTooltip, setImportTooltip] = useState(false);
  const [exportTooltip, setExportTooltip] = useState(false);
  const [exportByIdTooltip, setExportByIdTooltip] = useState(false);
  const [editContactsTooltip, setEditContactsTooltip] = useState(null);
  const [deleteContactsTooltip, setDeleteContactsTooltip] = useState(null);
  const [restoreContactsTooltip, setRestoreContactsTooltip] = useState(null);
  const [deleteContactsByIdTooltip, setDeleteContactsByIdTooltip] =
    useState(false);
  const [restoreContactsByIdTooltip, setRestoreContactsByIdTooltip] =
    useState(false);

  const [isEditContactLoading, setIsEditContactLoading] = useState(false);

  // states for selected contacts
  const [selectedContactIds, setSelectedContactIds] = useState<
    Array<(typeof teamData)[number]>
  >([]);

  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [isRestoreLoading, setIsRestoreLoading] = useState(false);

  const [isExportAllLoading, setIsExportAllLoading] = useState(false);
  const [isExportAllByIdLoading, setIsExportAllByIdLoading] = useState(false);
  const [isTableLoading, setIsTableLoading] = useState(false);
  const [isFilterLoading, setIsFilterLoading] = useState(false);
  const [isTeamDataActive, setIsTeamDataActive] = useState(true);

  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const [checkedIds, setCheckedIds] = useState<number[]>([]);

  const [dateRangeFilter, setDateRangeFilter] = useState<DateRange>({
    startDate: "",
    endDate: "",
  });

  const [selectedDateColumn, setSelectedDateColumn] = useState<{
    value: string;
    label: string;
  } | null>(null);
  const [trackerToolTip, setTrackerToolTip] = useState(false);
  const [columnFilters, setColumnFilters] = useState<Record<string, string>>(
    {}
  );

  const [tagsAnchorEl, setTagsAnchorEl] = useState<null | HTMLElement>(null);
  const [tagSearchQuery, setTagSearchQuery] = useState("");

  // this function is used to filter the main filter options for contacts
  const contactMainFilterOptions = mainFilterOptions?.filter(
    (eachFilter) => eachFilter.id !== "2"
  );
  // this function is used to create main filter options for contacts (removed Chat Status, Source, and Date as they're handled at column level)
  const contactMainFilterOptionsWithChatAndSource = [
    ...contactMainFilterOptions.filter(option => option.value !== "Date"),
  ];
  // this function is used to filter the sub filter options for contacts
  const contactSubFilterOptions = statusOptions?.filter(
    (eachFilter) => eachFilter.id === "5"
  );

  const hasAddContactPermission = (permission: any) => {
    for (const profileItem of permission) {
      if (Object.prototype.hasOwnProperty.call(profileItem, "addNewContact")) {
        return true;
      }
    }
  };

  //states for allInfo of contact in mobile view
  const [detailsMap, setDetailsMap] = useState<{ [key: string]: boolean }>({});
  const [anchorElMap, setAnchorElMap] = useState<{
    [key: string]: HTMLElement | null;
  }>({});

  //state for sync dailog

  const [syncAnchorEl, setSyncAnchorEl] = useState<null | HTMLElement>(null);

  // function to open the popover in mobile view
  const handleInfoClick = (
    event: React.MouseEvent<HTMLElement>,
    contactId: string
  ) => {
    setAnchorElMap((prev) => ({
      ...prev,
      [contactId]: event.currentTarget,
    }));
    setDetailsMap((prev) => ({
      ...prev,
      [contactId]: true,
    }));
  };
  //function to close the popover in mobile view
  const handleInfoClose = (contactId: string) => {
    setAnchorElMap((prev) => ({
      ...prev,
      [contactId]: null,
    }));
    setDetailsMap((prev) => ({
      ...prev,
      [contactId]: false,
    }));
  };

  //permissions
  const hasImportPermission = (permission: any) => {
    for (const profileItem of permission) {
      if (Object.prototype.hasOwnProperty.call(profileItem, "import")) {
        return true;
      }
    }
  };

  const hasExportPermission = (permission: any) => {
    for (const profileItem of permission) {
      if (Object.prototype.hasOwnProperty.call(profileItem, "export")) {
        return true;
      }
    }
  };

  const hasDeleteContactPermission = (permission: any) => {
    for (const profileItem of permission) {
      if (Object.prototype.hasOwnProperty.call(profileItem, "deleteContacts")) {
        return true;
      }
    }
  };

  const hasRestoreContactPermission = (permission: any) => {
    for (const profileItem of permission) {
      if (
        Object.prototype.hasOwnProperty.call(profileItem, "restoreContacts")
      ) {
        return true;
      }
    }
  };

  const hasExportByIdPermission = (permission: any) => {
    for (const profileItem of permission) {
      if (Object.prototype.hasOwnProperty.call(profileItem, "exportById")) {
        return true;
      }
    }
  };

  const hasEditContactPermission = (permission: any) => {
    for (const profileItem of permission) {
      if (Object.prototype.hasOwnProperty.call(profileItem, "editContacts")) {
        return true;
      }
    }
  };

  const handleFilterMove = (event: any) => {
    setAnchorElMove(event.currentTarget);
  };

  const handleCloseMove = () => {
    setAnchorElMove(null);
  };

  const openSyncSourcesDialog = (e: any) => {
    setSyncAnchorEl(e.currentTarget);
  };

  const closeSyncSourcesDialog = () => {
    setSyncAnchorEl(null);
  };

  const handleOpenDelete = (id: any) => {
    const hasPermission = hasDeleteContactPermission(manageContactsObject);
    if (hasPermission) {
      setDeleteDialog(true);
      setGetId(id);
    } else {
      setDeleteContactsTooltip(id);
      setTimeout(() => {
        setDeleteContactsTooltip(null);
      }, 2000);
    }
  };

  const handleCloseDelete = () => {
    setDeleteDialog(false);
  };

  const handleCloseSelectedDelete = () => {
    setDeleteSelectedDialog(false);
  };
  const handleCloseSelectedRestore = () => {
    setRestoreSelectedDialog(false);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCloseContactsFilterPopOver = () => {
    setAnchorElContact(null);
  };

  const handleDateRangeFilter = (dateRange: DateRange & { column: string }) => {
    setIsFilterLoading(true);
    setDateRangeFilter(dateRange);
    setPage(1);
    setCheckedIds([]);
    setSelectedContactIds([]);
    // Set the main filter option to the selected date column's label and icon
    const selectedCol = dateColumns.find(
      (col) => col.value === dateRange.column
    );
    if (selectedCol) {
      setSelectedContactFilter({
        id: dateRange.column,
        value: selectedCol.label,
        icon: <DateRangeIcon fontSize="small" />,
      });
    }
  };

  const handleColumnFilter = (columnId: string, selectedValue: string) => {
    setIsFilterLoading(true);
    setColumnFilters((prev) => ({
      ...prev,
      [columnId]: selectedValue,
    }));
    setPage(1);
    setCheckedIds([]);
    setSelectedContactIds([]);
  };

  const handleClearColumnFilter = (columnId: string) => {
    setIsFilterLoading(true);
    setColumnFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[columnId];
      return newFilters;
    });
    setPage(1);
    setCheckedIds([]);
    setSelectedContactIds([]);
  };

  const handleDelete = async () => {
    setIsDeleteLoading(true);

    try {
      const body = {
        BusinessId: userData?.companyId,
        userId: userData?.userId,
        data: [getId],
      };
      setSelectedContactIds([]);

      const deleteResponse: any = await dispatch(removeContact(body));
      if (deleteResponse?.meta?.requestStatus === "fulfilled") {
        setDeleteDialog(false);
        setGetId(null);

        const bodyData = {
          businessId: userData?.companyId,
          userId: userData?.userId,
          page: 1,
          per_page: 40,
          data: {
            searching: {
              value: searchContactsQuery,
            },
            sorting: {
              column: "",
              order: "",
            },
            filtering: {
              filterType: "or",
              conditions: [
                {
                  column: selectedFilter || "Tags",
                  operator: "contains",
                  value: [],
                },
              ],
            },
          },
        };
        if (isTeamDataActive) {
          dispatch(fetchAllContacts(bodyData));
        } else {
          const bodyData = {
            page: 1,
            per_page: 40,
            filters: {
              searching: {
                value: searchContactsQuery,
              },
              sorting: {
                column: "",
                order: "",
              },
              filtering: {
                filterType: "or",
                conditions: [
                  {
                    column: selectedFilter || "Tags",
                    operator: "contains",
                    value: [],
                  },
                ],
              },
            },
          };
          dispatch(fetchAllDeletedContacts(bodyData));
        }

        dispatch(
          toastActions.setToaster({
            type: "success",
            message: `${deleteResponse?.payload?.message}`,
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: `${deleteResponse?.payload?.message}`,
          })
        );
      }
    } catch (error: any) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: `${error?.response?.data?.message}`,
        })
      );
    } finally {
      setIsDeleteLoading(false);
    }
  };

  const handleRestore = async (id: string) => {
    // setIsRestoreLoading(true);

    try {
      const body = {
        businessId: userData?.companyId,
        userId: userData?.userId,
        contactIds: [id],
      };

      setSelectedContactIds([]);
      setIsTeamDataActive(false);
      const restoreResponse: any = await dispatch(
        fetchAllRestoredContacts(body)
      );
      if (restoreResponse?.meta?.requestStatus === "fulfilled") {
        // setDeletedContacts(allDeletedContactsData?.deleteContacts);
        setRestoreDialog(false);
        setGetId(null);

        const bodyData = {
          businessId: userData?.companyId,
          userId: userData?.userId,
          page: 1,
          per_page: 40,
          filters: {
            searching: {
              value: searchContactsQuery,
            },
            sorting: {
              column: "",
              order: "",
            },
            filtering: {
              filterType: "or",
              conditions: [
                {
                  column: selectedFilter || "Tags",
                  operator: "contains",
                  value: [],
                },
              ],
            },
          },
        };
        dispatch(fetchAllDeletedContacts(bodyData));

        dispatch(
          toastActions.setToaster({
            type: "success",
            message: `${restoreResponse?.payload?.message}`,
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: `${restoreResponse?.payload?.message}`,
          })
        );
      }
    } catch (error: any) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: `${error?.response?.data?.message}`,
        })
      );
    } finally {
      // setIsRestoreLoading(false);
    }
  };

  const handleDeleteById = async () => {
    const body = {
      BusinessId: userData?.companyId,
      userId: userData?.userId,
      data: selectedContactIds,
    };
    setIsDeleteLoading(true);
    const deleteResponse = await CONTACTS_APIS.deleteContacts(body);
    if (deleteResponse?.status === 200) {
      setDeleteDialog(false);
      setDeleteSelectedDialog(false);
      setGetId(null);
      setSelectedContactIds([]);

      const bodyData = {
        businessId: userData?.companyId,
        userId: userData?.userId,
        page: 1,
        per_page: 40,
        // filters: {},
      };
      dispatch(fetchAllContacts(bodyData));

      // toast.success(deleteResponse?.data?.message);
      dispatch(
        toastActions.setToaster({
          type: "success",
          message: `${deleteResponse?.data?.message}`,
        })
      );
    } else {
      toast.error(deleteResponse?.data?.message);
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: `${deleteResponse?.data?.message}`,
        })
      );
    }
    setIsDeleteLoading(false);
  };

  const handleRestoreById = async () => {
    const body = {
      businessId: userData?.companyId,
      userId: userData?.userId,
      contactIds: selectedContactIds,
    };
    setIsRestoreLoading(true);
    const restoreResponse = await CONTACTS_APIS.getRestoredContacts(body);
    if (restoreResponse?.status === 200) {
      setRestoreDialog(false);
      setRestoreSelectedDialog(false);
      setGetId(null);
      const bodyData = {
        businessId: userData?.companyId,
        userId: userData?.userId,
        page: 1,
        per_page: 40,
        filters: {
          searching: {
            value: searchContactsQuery,
          },
          sorting: {
            column: "",
            order: "",
          },
          filtering: {
            filterType: "or",
            conditions: [
              {
                column: selectedFilter || "Tags",
                operator: "contains",
                value: [],
              },
            ],
          },
        },
      };
      dispatch(fetchAllDeletedContacts(bodyData));

      // toast.success(deleteResponse?.data?.message);
      dispatch(
        toastActions.setToaster({
          type: "success",
          message: `${restoreResponse?.data?.message}`,
        })
      );
    } else {
      toast.error(restoreResponse?.data?.message);
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: `${restoreResponse?.data?.message}`,
        })
      );
    }
    setSelectedContactIds([]);
    setIsRestoreLoading(false);
  };

  const handleDeleteAll = () => {
    const hasPermission = hasDeleteContactPermission(manageContactsObject);
    if (hasPermission) {
      setDeleteSelectedDialog(true);
    } else {
      setDeleteContactsByIdTooltip(true);
      setTimeout(() => {
        setDeleteContactsByIdTooltip(false);
      }, 2000);
    }
  };

  const handleRestoreAll = () => {
    const hasPermission = hasDeleteContactPermission(manageContactsObject);
    if (hasPermission) {
      setRestoreSelectedDialog(true);
    } else {
      setRestoreContactsByIdTooltip(true);
      setTimeout(() => {
        setRestoreContactsByIdTooltip(false);
      }, 2000);
    }
  };

  const handleOpenDialog = () => {
    const hasPermission = hasAddContactPermission(manageContactsObject);
    if (hasPermission) {
      setOpenDialog(true);
    } else {
      setAddContactTooltip(true);
      setTimeout(() => {
        setAddContactTooltip(false);
      }, 2000);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleOpenEditDialog = (row?: any) => {
    const hasPermission = hasEditContactPermission(manageContactsObject);
    if (hasPermission) {
      if (row !== undefined && row !== null) {
        setEditContact(row);
      }
      setOpenEditDialog(true);
    } else {
      setEditContactsTooltip(row);
      setTimeout(() => {
        setEditContactsTooltip(null);
      }, 2000);
    }
  };

  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
  };

  const handleExport = async () => {
    const hasPermission = hasExportPermission(manageContactsObject);
    if (hasPermission) {
      const data = {
        businessId: userData?.companyId,
        userId: userData?.userId,
      };
      try {
        setIsExportAllLoading(true);
        const exportResponse = await CONTACTS_APIS.exportContacts(data);
        // Extract the binary data from the AxiosResponse object
        const blob = new Blob([exportResponse.data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");

        link.href = url;
        link.download = "exported_contacts.xlsx";
        link.style.display = "none";
        document.body.appendChild(link);

        link.click();

        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        setIsExportAllLoading(false);
      } catch (error) {
        setIsExportAllLoading(false);
      }
    } else {
      setExportTooltip(true);
      setTimeout(() => {
        setExportTooltip(false);
      }, 2000);
      setIsExportAllLoading(false);
    }
  };

  const handleExportById = async () => {
    const hasPermission = hasExportByIdPermission(manageContactsObject);
    if (hasPermission) {
      if (selectedContactIds?.length > 0) {
        const data = {
          businessId: userData?.companyId,
          userId: userData?.userId,
          contactId: selectedContactIds,
        };
        try {
          setIsExportAllByIdLoading(true);
          const getResponseById = await CONTACTS_APIS.exportContactsById(data);
          const blob = new Blob([getResponseById.data], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });

          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");

          link.href = url;
          link.download = "exported_contacts.xlsx";
          link.style.display = "none";
          document.body.appendChild(link);

          link.click();

          // Cleanup
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
          setIsExportAllByIdLoading(false);
        } catch (error) {
          setIsExportAllByIdLoading(false);
        }
      }
    } else {
      setExportByIdTooltip(true);
      setTimeout(() => {
        setExportByIdTooltip(false);
      }, 2000);
      setIsExportAllByIdLoading(false);
    }
  };

  const handleDialog = () => {
    const hasPermission = hasImportPermission(manageContactsObject);
    if (hasPermission) {
      setDialog(true);
    } else {
      setImportTooltip(true);
      setTimeout(() => {
        setImportTooltip(false);
      }, 2000);
    }
  };

  const handleTrackerDialog = () => {
    const hasPermission = hasImportPermission(manageContactsObject);
    if (hasPermission) {
      setTrackerDialog(true);
    } else {
      setTrackerToolTip(true);
      setTimeout(() => {
        setTrackerToolTip(false);
      }, 2000);
    }
  };

  const handleClosed = () => {
    setDialog(false);
  };

  const handleTrackerClosed = () => {
    setTrackerDialog(false);
  };
  const debouncedFetchContacts = useCallback(
    debounce(async (bodyData) => {
      setIsTableLoading(true);
      try {
        await dispatch(fetchAllContacts(bodyData));
      } catch (error) {
        console.error("Error fetching contacts:", error);
      } finally {
        setIsTableLoading(false);
      }
    }, 500),
    []
  );

  const debouncedFetchDeletedContacts = useCallback(
    debounce(async (data) => {
      setIsTableLoading(true);
      try {
        await dispatch(fetchAllDeletedContacts(data));
      } catch (error) {
        console.error("Error fetching deleted contacts:", error);
      } finally {
        setIsTableLoading(false);
      }
    }, 500),
    []
  );

  const debouncedFetchContactsTagsDetails = useCallback(
    debounce(async (data) => {
      dispatch(getContactTags(data));
    }, 500),
    []
  );

  const handleAllFilterOptionClick = async (option: SelectedFilter) => {
    console.log("option", option);
    setIsTableLoading(true);
    setIsFilterLoading(true);

    if (selectedContactFilter.id === option.id) {
      handleCloseContactsFilterPopOver();
      setIsTableLoading(false);
      setIsFilterLoading(false);
      return;
    }
    setSelectedContactFilter(option);

    try {
      const isArchivedFilter = option.id.startsWith("Status-5");
      const isViewAll = option.value === "View All";
      const hasColumnFilters = Object.keys(columnFilters).length > 0 || (dateRangeFilter.startDate && dateRangeFilter.endDate);
      
      if (isViewAll) {
        // Clear all column filters when "View All" is selected
        setColumnFilters({});
        setDateRangeFilter({ startDate: "", endDate: "" });
        setSelectedDateColumn(null);
        setDeletedContacts([]);
        setIsTeamDataActive(true);
        setPage(1);
        setSearchContactsQuery("");
        setCheckedIds([]);
        setSelectedContactIds([]);
        
        // Only make API call if column filters were applied
        if (hasColumnFilters) {
          // The API call will be triggered by the useEffect when columnFilters change
        } else {
          // No API call needed - we already have all contacts
          setIsTableLoading(false);
          setIsFilterLoading(false);
          handleCloseContactsFilterPopOver();
          return;
        }
      } else if (isArchivedFilter) {
        // Clear all column filters when switching to archived status
        setColumnFilters({});
        setDateRangeFilter({ startDate: "", endDate: "" });
        setSelectedDateColumn(null);
        setIsTeamDataActive(false);
        setTeamData([]);
        setPage(1);
        setCheckedIds([]);
        setSelectedContactIds([]);
        setSearchContactsQuery("");
        const data = {
          businessId: userData?.companyId,
          userId: userData?.userId,
          page: page,
          per_page: perPage,
          filters: {
            searching: {
              value: searchContactsQuery,
            },
            filtering: {
              filterType: "or",
              conditions: [
                {
                  column: "Tags",
                  operator: "contains",
                  value: [],
                },
              ],
            },
          },
        };
        const response = await dispatch(fetchAllDeletedContacts(data));
        console.log("response", response);
      } else {
        // For all other filters (Tags, etc.), just update the filter state
        // The actual API call will be handled by fetchContacts useEffect
        setDeletedContacts([]);
        setIsTeamDataActive(true);
        setPage(1);
        setSearchContactsQuery("");
        setCheckedIds([]);
        setSelectedContactIds([]);
      }
    } finally {
      setIsTableLoading(false);
      setIsFilterLoading(false);
      handleCloseContactsFilterPopOver();
    }
  };

  const fetchContacts = async () => {
    setIsFilterLoading(false);
    if (searchContactsQuery) {
      setTeamData([]);
      setDeletedContacts([]);
    }

    // Build filter conditions based on selectedContactFilter and columnFilters
    const filterConditions = [];

    // Handle main filter (Tags, etc.)
    if (selectedContactFilter.id.startsWith("Status-")) {
      // Handle existing Status filter
      filterConditions.push({
        column: selectedFilter || "Tags",
        operator: "contains",
        value: [],
      });
    } else {
      // Default filter for Tags and other main filters
      filterConditions.push({
        column: selectedFilter || "Tags",
        operator: "contains",
        value: [],
      });
    }

    // Add column filters (ChatStatus, Source, etc.)
    const chatStatusValues: string[] = [];
    const sourceValues: string[] = [];
    let dateRangeForApi: any = null;

    Object.entries(columnFilters).forEach(([columnId, selectedValue]) => {
      if (selectedValue) {
        if (columnId === "createdDate") {
          try {
            dateRangeForApi = JSON.parse(selectedValue);
          } catch {}
        } else if (columnId === "chatStatus") {
          // Map string values to backend expected values
          let mappedValue;
          switch (selectedValue) {
            case "Open":
              mappedValue = "open";
              break;
            case "Resolved":
              mappedValue = "resolved";
              break;
            case "Expired":
              mappedValue = "expired";
              break;
            case "New":
              mappedValue = "New";
              break;
            default:
              mappedValue = selectedValue.toLowerCase();
          }
          chatStatusValues.push(mappedValue);
        } else if (columnId === "source") {
          // Map string values to backend expected values
          let mappedValue;
          switch (selectedValue) {
            case "None":
              mappedValue = "None";
              break;
            case "Direct":
              mappedValue = "Direct";
              break;
            case "WhatsApp":
              mappedValue = "WhatsApp";
              break;
            case "Leadrat":
              mappedValue = "LeadRat";
              break;
            case "Excell":
              mappedValue = "Excel";
              break;
            default:
              mappedValue = selectedValue;
          }
          sourceValues.push(mappedValue);
        } else {
          filterConditions.push({
            column: columnId,
            operator: "equals",
            value: selectedValue,
          });
        }
      }
    });

    // Add ChatStatus condition if any values are selected
    if (chatStatusValues.length > 0) {
      filterConditions.push({
        column: "ChatStatus",
        operator: "equals",
        value: chatStatusValues,
      });
    }

    // Add Source condition if any values are selected
    if (sourceValues.length > 0) {
      filterConditions.push({
        column: "Source",
        operator: "equals",
        value: sourceValues,
      });
    }

    const bodyData = {
      businessId: userData?.companyId,
      userId: userData?.userId,
      page: page,
      per_page: perPage,
      data: {
        searching: {
          value: searchContactsQuery,
        },
        filtering: {
          filterType: "and",
          conditions: [...filterConditions],
        },
        dateRangeFilters: dateRangeForApi
          ? [
              {
                column: "CreatedDate",
                fromDate: moment(`${dateRangeForApi.startDate}T00:00:00Z`)
                  .utc()
                  .format(),
                toDate: moment(`${dateRangeForApi.endDate}T00:00:00Z`)
                  .utc()
                  .format(),
              },
            ]
          : dateRangeFilter.startDate && dateRangeFilter.endDate
          ? [
              {
                column: "CreatedDate",
                fromDate: moment(`${dateRangeFilter.startDate}T00:00:00Z`)
                  .utc()
                  .format(),
                toDate: moment(`${dateRangeFilter.endDate}T00:00:00Z`)
                  .utc()
                  .format(),
              },
            ]
          : [],
      },
    };
    try {
      if (isTeamDataActive) {
        if (searchContactsQuery) {
          await debouncedFetchContacts(bodyData);
        } else {
          await dispatch(fetchAllContacts(bodyData));
        }
      } else {
        const data = {
          businessId: userData?.companyId,
          userId: userData?.userId,
          filters: {
            searching: {
              value: searchContactsQuery,
            },
            filtering: {
              filterType: "and",
              conditions: [...filterConditions],
            },
            dateRangeFilters: dateRangeForApi
              ? [
                  {
                    column: "CreatedDate",
                    fromDate: moment(`${dateRangeForApi.startDate}T00:00:00Z`)
                      .utc()
                      .format(),
                    toDate: moment(`${dateRangeForApi.endDate}T00:00:00Z`)
                      .utc()
                      .format(),
                  },
                ]
              : dateRangeFilter.startDate && dateRangeFilter.endDate
              ? [
                  {
                    column: "CreatedDate",
                    fromDate: moment(`${dateRangeFilter.startDate}T00:00:00Z`)
                      .utc()
                      .format(),
                    toDate: moment(`${dateRangeFilter.endDate}T00:00:00Z`)
                      .utc()
                      .format(),
                  },
                ]
              : [],
          },
          page: page,
          per_page: perPage,
        };
        await debouncedFetchDeletedContacts(data);
      }
    } catch (err) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: `Error loading Contacts`,
        })
      );
    }
  };

  useEffect(() => {
    fetchContacts();
  }, [
    searchContactsQuery,
    selectedFilter,
    selectedContactFilter,
    page,
    perPage,
    dateRangeFilter,
    columnFilters,
  ]);

  useEffect(() => {
    const body = {
      businessId: userData?.companyId,
      userId: userData?.userId,
      search: searchContactsQuery,
    };
    debouncedFetchContactsTagsDetails(body);
  }, [searchContactsQuery]);

  const formatDate = (datetime: any) => {
    const date = new Date(datetime);
    const year = date.getFullYear();
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[date.getMonth()];
    const day = ("0" + date.getDate()).slice(-2);
    return `${day} ${month} ${year}`;
  };

  useEffect(() => {
    if (contactsData?.data) {
      const newData = contactsData.data.map((item: any) => ({
        ...item,
        isChecked: selectAllChecked,
      }));

      setTeamData(newData);

      if (selectAllChecked) {
        setSelectedContactIds((prevCheckedItems) => [
          ...prevCheckedItems,
          ...newData,
        ]);
      }
    }
  }, [contactsData?.data]);

  useEffect(() => {
    if (deletedContactsData?.data) {
      setDeletedContacts(deletedContactsData.data?.items);
    }
  }, [deletedContactsData?.data?.items]);

  const handleContactsAllFilter = (event: any) => {
    setAnchorElContact(event.currentTarget);
  };

  const handleChangePage = (
    event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
  };

  // Tags filter handling
  const handleTagsMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setTagsAnchorEl(event.currentTarget);
  };

  const handleTagsMenuClose = () => {
    setTagsAnchorEl(null);
    setTagSearchQuery("");
  };

  const allTagsFromData = Array.from(
    new Set(
      getContactTagsData?.flatMap((contact: any) => {
        return {
          id: contact.id,
          tag: contact.tag,
        };
      })
    )
  ).sort((a: any, b: any) => a.tag.localeCompare(b.tag));

  const filteredTags = allTagsFromData.filter((tag: any) =>
    tag.tag.toLowerCase().includes(tagSearchQuery.toLowerCase())
  );

  const columns: any[] = [
    {
      id: "name",
      label: "Name",
      disablePadding: true,
    },
    {
      id: "chatStatus",
      label: "Chat status",
      filterable: true,
      filterOptions: chatStatusOptions,
      format: (value: any) => (
        <Chip
          label={
            value === 0
              ? "Open"
              : value === 1
              ? "Resolved"
              : value === 2
              ? "Expired"
              : "New"
          }
          size="small"
          sx={{
            backgroundColor:
              value === 0
                ? "#dcfce7"
                : value === 1
                ? "#f2fff2"
                : value === 2
                ? "#fff5f5"
                : " #f7f3ff",
            color:
              value === 0
                ? "#16a34a"
                : value === 1
                ? "#1073e7"
                : value === 2
                ? "#ef4444"
                : "#3b82f6",
            fontWeight: "normal",
            fontSize: "0.75rem",
          }}
        />
      ),
    },
    {
      id: "source",
      label: "Source",
      filterable: true,
      filterOptions: sourceOptions,
      format: (value: any) => (
        <Chip
          label={
            value === 0
              ? "None"
              : value === 1
              ? "Direct"
              : value === 2
              ? "WhatsApp"
              : value === 3
              ? "Leadrat"
              : value === 4
              ? "Excell"
              : "Unknown"
          }
          size="small"
          sx={{
            ...getStylesByValue(value),
            fontWeight: "normal",
            fontSize: "0.75rem",
          }}
        />
      ),
    },
    { id: "contact", label: "Contact" },
    { id: "email", label: "Email" },
    { id: "country", label: "Country" },
    {
      id: "createdDate",
      label: "Created Date",
      filterable: true,
      filterOptions: [], // Will be handled by date filter
      format: (value: any) => formatDate(value),
    },
    {
      id: "tags",
      label: "Tags",
      truncate: {
        maxItems: 1,
        tooltipPlacement: "top",
      },
    },
  ];
  const deletedContactsColumns: any[] = [
    {
      id: "name",
      label: "Name",
      disablePadding: true,
    },
    {
      id: "chatStatus",
      label: "Chat status",
      format: (value: any) => (
        <Chip
          label={
            value === 0
              ? "Open"
              : value === 1
              ? "Resolved"
              : value === 2
              ? "Expired"
              : "New"
          }
          size="small"
          sx={{
            backgroundColor:
              value === 0
                ? "#dcfce7"
                : value === 1
                ? "#f2fff2"
                : value === 2
                ? "#fff5f5"
                : " #f7f3ff",
            color:
              value === 0
                ? "#16a34a"
                : value === 1
                ? "#1073e7"
                : value === 2
                ? "#ef4444"
                : "#3b82f6",
            fontWeight: "normal",
            fontSize: "0.75rem",
          }}
        />
      ),
    },
    {
      id: "source",
      label: "Source",
      format: (value: any) => (
        <Chip
          label={
            value === 0
              ? "None"
              : value === 1
              ? "Direct"
              : value === 2
              ? "WhatsApp"
              : value === 3
              ? "Leadrat"
              : value === 4
              ? "Excell"
              : "Unknown"
          }
          size="small"
          sx={{
            ...getStylesByValue(value),
            fontWeight: "normal",
            fontSize: "0.75rem",
          }}
        />
      ),
    },
    { id: "contact", label: "Contact" },
    { id: "email", label: "Email" },
    { id: "countryName", label: "Country" },
    {
      id: "createdDate",
      label: "Created Date",
      format: (value: any) => formatDate(value),
    },
    {
      id: "tags",
      subId: "tagName",
      label: "Tags",
      truncate: {
        maxItems: 1,
        tooltipPlacement: "top",
      },
    },
  ];

  const dateColumns = [{ value: "createdDate", label: "Created Date" }];

  const renderActions = (row: any) => (
    <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-start" }}>
      <IconButton
        onClick={() => handleOpenEditDialog(row)}
        size="small"
        sx={{ color: "#666", "&:hover": { color: "#3b82f6" } }}
      >
        <EditIcon fontSize="small" />
      </IconButton>
      <IconButton
        onClick={() => handleOpenDelete(row?.contactId)}
        size="small"
        sx={{ color: "#666", "&:hover": { color: "#ef4444" } }}
      >
        <DeleteIcon fontSize="small" />
      </IconButton>
    </Box>
  );

  const renderActionsForDeletedContacts = (row: any) => (
    <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-start" }}>
      <IconButton
        onClick={() => handleRestore(row?.contactId)}
        size="small"
        sx={{ color: "#666", "&:hover": { color: "#ef4444" } }}
      >
        <RestoreIconSvg />
      </IconButton>
    </Box>
  );

  const renderContactsInMobile = (
    row: any,
    isItemSelected: boolean,
    handleCheckboxClick: any
  ) => {
    return (
      <>
        <Card key={row.contactId} sx={{ padding: 2 }}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              marginBottom: 8,
            }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <Checkbox
                id={`check-${row.contactId}`}
                checked={isItemSelected}
                onClick={handleCheckboxClick}
              />
            </div>
            <div style={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Chip
                label={
                  row.chatStatus === 0
                    ? "Open"
                    : row.chatStatus === 1
                    ? "Resolved"
                    : row.chatStatus === 2
                    ? "Expired"
                    : "New"
                }
                size="small"
                sx={{
                  backgroundColor:
                    row.chatStatus === 0
                      ? "#dcfce7"
                      : row.chatStatus === 1
                      ? "#f2fff2"
                      : row.chatStatus === 2
                      ? "#fff5f5"
                      : " #f7f3ff",
                  color:
                    row.chatStatus === 0
                      ? "#16a34a"
                      : row.chatStatus === 1
                      ? "#1073e7"
                      : row.chatStatus === 2
                      ? "#ef4444"
                      : "#3b82f6",
                  fontWeight: "normal",
                  fontSize: "0.75rem",
                }}
              />
              <IconButton
                size="small"
                onClick={(e) => handleInfoClick(e, row.contactId)}
                sx={{
                  color: "#666",
                  "&:hover": { color: "#3b82f6" },
                }}
              >
                <InfoOutlinedIcon fontSize="small" />
              </IconButton>
            </div>
          </div>

          {/* Basic info always visible */}
          <CardContent
            sx={{ padding: 0, "&:last-child": { paddingBottom: 0 } }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                fontSize: 14,
                marginBottom: 8,
              }}
            >
              <Typography color="text.secondary">Name:</Typography>
              <Typography>{row.name || "N/A"}</Typography>
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                fontSize: 14,
                marginBottom: 8,
              }}
            >
              <Typography color="text.secondary">Contact:</Typography>
              <Typography>{row.contact}</Typography>
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 8,
                marginTop: 16,
              }}
            >
              <Button
                variant="outlined"
                startIcon={<EditIcon />}
                onClick={() => handleOpenEditDialog(row)}
                size="small"
              >
                Edit
              </Button>
              <Button
                variant="outlined"
                color="error"
                startIcon={<DeleteIcon />}
                onClick={() => handleOpenDelete(row?.contactId)}
                size="small"
              >
                Delete
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Detailed info in Popover */}
        <Popover
          open={Boolean(detailsMap[row.contactId])}
          anchorEl={anchorElMap[row.contactId]}
          onClose={() => handleInfoClose(row.contactId)}
          anchorOrigin={{
            vertical: "center",
            horizontal: "right",
          }}
          transformOrigin={{
            vertical: "center",
            horizontal: "left",
          }}
          PaperProps={{
            sx: {
              maxWidth: "300px",
              p: 2,
              boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
            },
          }}
        >
          <Box sx={{ minWidth: 250, position: "relative" }}>
            <IconButton
              onClick={() => handleInfoClose(row.contactId)}
              sx={{
                position: "absolute",
                top: 0,
                right: 0,
                color: "red", // or your theme-based color
              }}
              size="small"
              aria-label="close"
            >
              <RedCloseIcon />{" "}
            </IconButton>
            <Grid container spacing={1}>
              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Name
                </Typography>
                <Typography>{row.name || "N/A"}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Chat Status
                </Typography>
                <Typography>
                  {
                    <Chip
                      label={
                        row.chatStatus === 0
                          ? "Open"
                          : row.chatStatus === 1
                          ? "Resolved"
                          : row.chatStatus === 2
                          ? "Expired"
                          : "New"
                      }
                      size="small"
                      sx={{
                        backgroundColor:
                          row.chatStatus === 0
                            ? "#dcfce7"
                            : row.chatStatus === 1
                            ? "#f2fff2"
                            : row.chatStatus === 2
                            ? "#fff5f5"
                            : " #f7f3ff",
                        color:
                          row.chatStatus === 0
                            ? "#16a34a"
                            : row.chatStatus === 1
                            ? "#1073e7"
                            : row.chatStatus === 2
                            ? "#ef4444"
                            : "#3b82f6",
                        fontWeight: "normal",
                        fontSize: "0.75rem",
                      }}
                    />
                  }
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Email
                </Typography>
                <Typography>{row.email || "N/A"}</Typography>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Country
                </Typography>
                <Typography>{row.country || "N/A"}</Typography>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Source
                </Typography>
                <Typography>
                  <Chip
                    label={
                      row.source === 0
                        ? "None"
                        : row.source === 1
                        ? "Direct"
                        : row.source === 2
                        ? "WhatsApp"
                        : row.source === 3
                        ? "Leadrat"
                        : row.source === 4
                        ? "Excell"
                        : "Unknown"
                    }
                    size="small"
                    sx={{
                      ...getStylesByValue(row.source),
                      fontWeight: "normal",
                      fontSize: "0.75rem",
                    }}
                  />
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Created Date
                </Typography>
                <Typography>{formatDate(row.createdDate)}</Typography>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Tags
                </Typography>
                <Box
                  sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, mt: 0.5 }}
                >
                  {row.tags.length > 0 ? (
                    row.tags.map((tag: any, index: number) => (
                      <Chip
                        key={index}
                        label={tag.tag}
                        size="small"
                        sx={{ fontSize: "0.75rem" }}
                      />
                    ))
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No tags
                    </Typography>
                  )}
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Popover>
      </>
    );
  };
  const renderDeletedContactsInMobile = (
    row: any,
    isItemSelected: boolean,
    handleCheckboxClick: any
  ) => {
    return (
      <>
        <Card key={row.contactId} sx={{ padding: 2, mb: 2 }}>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: 16,
            }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <Checkbox
                checked={isItemSelected}
                onChange={(event) => handleCheckboxClick(event, row.contactId)}
                size="small"
              />
              <Chip
                label={
                  row.chatStatus === 0
                    ? "Open"
                    : row.chatStatus === 1
                    ? "Resolved"
                    : row.chatStatus === 2
                    ? "Expired"
                    : "New"
                }
                size="small"
                sx={{
                  backgroundColor:
                    row.chatStatus === 0
                      ? "#dcfce7"
                      : row.chatStatus === 1
                      ? "#f2fff2"
                      : row.chatStatus === 2
                      ? "#fff5f5"
                      : " #f7f3ff",
                  color:
                    row.chatStatus === 0
                      ? "#16a34a"
                      : row.chatStatus === 1
                      ? "#1073e7"
                      : row.chatStatus === 2
                      ? "#ef4444"
                      : "#3b82f6",
                  fontWeight: "normal",
                  fontSize: "0.75rem",
                }}
              />
            </div>
            <IconButton
              size="small"
              onClick={(e) => handleInfoClick(e, row.contactId)}
              sx={{
                color: "#666",
                "&:hover": { color: "#3b82f6" },
              }}
            >
              <InfoOutlinedIcon fontSize="small" />
            </IconButton>
          </div>

          <CardContent
            sx={{ padding: 0, "&:last-child": { paddingBottom: 0 } }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                fontSize: 14,
                marginBottom: 8,
              }}
            >
              <Typography color="text.secondary">Name:</Typography>
              <Typography>{row.name || "N/A"}</Typography>
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                fontSize: 14,
                marginBottom: 8,
              }}
            >
              <Typography color="text.secondary">Contact:</Typography>
              <Typography>{row.contact}</Typography>
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                fontSize: 14,
                marginBottom: 8,
              }}
            >
              <Typography color="text.secondary">Created Date:</Typography>
              <Typography>{formatDate(row.createdDate)}</Typography>
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 8,
                marginTop: 16,
              }}
            >
              <Button
                variant="outlined"
                startIcon={<RestoreIconSvg />}
                onClick={() => handleRestore(row?.contactId)}
                size="small"
                sx={{
                  borderColor: "#3b82f6",
                  color: "#3b82f6",
                  "&:hover": {
                    borderColor: "#2563eb",
                    backgroundColor: "rgba(59, 130, 246, 0.04)",
                  },
                }}
              >
                Restore
              </Button>
            </div>
          </CardContent>
        </Card>

        <Popover
          open={Boolean(detailsMap[row.contactId])}
          anchorEl={anchorElMap[row.contactId]}
          onClose={() => handleInfoClose(row.contactId)}
          anchorOrigin={{
            vertical: "center",
            horizontal: "right",
          }}
          transformOrigin={{
            vertical: "center",
            horizontal: "left",
          }}
          PaperProps={{
            sx: {
              maxWidth: "300px",
              p: 2,
              boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
            },
          }}
        >
          <Box sx={{ minWidth: 250, position: "relative" }}>
            <IconButton
              onClick={() => handleInfoClose(row.contactId)}
              sx={{
                position: "absolute",
                top: 0,
                right: 0,
                color: "red",
              }}
              size="small"
              aria-label="close"
            >
              <RedCloseIcon />
            </IconButton>
            <Grid container spacing={1}>
              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Name
                </Typography>
                <Typography>{row.name || "N/A"}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Email
                </Typography>
                <Typography>{row.email || "N/A"}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Country
                </Typography>
                <Typography>{row.countryName || "N/A"}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Source
                </Typography>
                <Typography>
                  <Chip
                    label={
                      row.source === 0
                        ? "None"
                        : row.source === 1
                        ? "Direct"
                        : row.source === 2
                        ? "WhatsApp"
                        : row.source === 3
                        ? "Leadrat"
                        : row.source === 4
                        ? "Excell"
                        : "Unknown"
                    }
                    size="small"
                    sx={{
                      ...getStylesByValue(row.source),
                      fontWeight: "normal",
                      fontSize: "0.75rem",
                    }}
                  />
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Created Date
                </Typography>
                <Typography>{formatDate(row.createdDate)}</Typography>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="caption" color="text.secondary">
                  Tags
                </Typography>
                <Box
                  sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, mt: 0.5 }}
                >
                  {row.tags.length > 0 ? (
                    row.tags.map((tag: any, index: number) => (
                      <Chip
                        key={index}
                        label={tag.tagName}
                        size="small"
                        sx={{ fontSize: "0.75rem" }}
                      />
                    ))
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No tags
                    </Typography>
                  )}
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Popover>
      </>
    );
  };

  const [openMoveToTagsDialog, setOpenMoveToTagsDialog] = useState(false);

  const handleMoveToTagsDialog = () => {
    setOpenMoveToTagsDialog(true);
  };

  const selectedItemsToolbarActionsForDeletedContacts = [
    {
      label: "Restore",
      icon: <RestoreIconSvg />,
      onClick: handleRestoreAll,
      show:
        hasRestoreContactPermission(manageContactsObject) && !isTeamDataActive,
    },
  ];

  const selectedItemsToolbarActions = [
    {
      label: "Move to Tags",
      icon: <MdDriveFileMoveOutline />,
      onClick: handleFilterMove,
      show:
        hasDeleteContactPermission(manageContactsObject) && isTeamDataActive,
    },
    {
      label: "Export",
      icon: <CgExport />,
      onClick: handleExportById,
      show:
        hasDeleteContactPermission(manageContactsObject) && isTeamDataActive,
    },
    {
      label: "Delete",
      icon: <DeleteIcon />,
      onClick: handleDeleteAll,
      show:
        hasDeleteContactPermission(manageContactsObject) && isTeamDataActive,
    },
  ];

  const transformedData = teamData.map((item: any) => ({
    ...item,
    tags: item.tags.filter((tag: any) => tag.tag && tag.tag.trim() !== ""),
  }));

  return (
    <>
      {currentPlanDetails?.subscriptionPlan?.isActive &&
      ((accountData?.companyVerificationStatus === true &&
        accountData?.isMetaEnabled) ||
        (accountData?.isMetaEnabled === false &&
          accountData?.companyVerificationStatus === false &&
          currentPlanDetails?.subscriptionPlan?.planName === "Intro")) &&
      getuserPermissionData?.contacts ? (
        <Grid className={classes.bgContainer}>
          {isTeamDataActive ? (
            <>
              <CommonTable
                columns={columns}
                data={transformedData}
                isLoading={isTableLoading || isFilterLoading || contactsLoading || deletedContactsLoading}
                rowIdKey="contactId"
                title="Contacts"
                count={contactsData?.total}
                selectable={true}
                onDelete={handleDeleteAll}
                actions={renderActions}
                page={page}
                onPageChange={handleChangePage}
                totalPages={Math.ceil(contactsData?.total / perPage)}
                selectedMainFilter={
                  selectedContactIds.length > 0
                    ? { id: "", value: "" }
                    : selectedContactFilter
                }
                handleMainFilter={
                  selectedContactIds.length > 0
                    ? () => {}
                    : handleContactsAllFilter
                }
                renderOnMobile={renderContactsInMobile}
                searchProps={{
                  value: searchContactsQuery,
                  onChange: setSearchContactsQuery,
                  placeholder: "Search contacts...",
                }}
                primaryAction={{
                  label: "Add Contact",
                  onClick: handleOpenDialog,
                  icon: <AddIcon />,
                  show: hasAddContactPermission(manageContactsObject),
                }}
                defaultToolbarActions={[
                  {
                    icon: <LuImport />,
                    label: "Import",
                    onClick: handleDialog,
                    show:
                      hasImportPermission(manageContactsObject) &&
                      isTeamDataActive,
                  },
                  {
                    icon: <CgExport />,
                    label: "Export",
                    onClick: handleExport,
                    show: hasExportPermission(manageContactsObject),
                  },
                  {
                    icon: <TrackChangesIcon />,
                    label: "Track Changes",
                    onClick: handleTrackerDialog,
                    show: isTeamDataActive,
                  },
                  {
                    icon: <SyncIcon />,
                    label: "Sync",
                    onClick: openSyncSourcesDialog,
                    show: isTeamDataActive,
                  },
                ]}
                selectedItemsToolbarActions={selectedItemsToolbarActions}
                tagsProps={{
                  onTagsClick: handleTagsMenuOpen,
                  tagsAnchorEl: tagsAnchorEl,
                  onTagsClose: handleTagsMenuClose,
                  tagsList: allTagsFromData,
                  show: selectedContactIds.length === 0,
                }}
                selectedItemIds={selectedContactIds}
                setSelectedItemIds={setSelectedContactIds}
                perPage={perPage}
                perPageOptions={[10, 25, 50, 100, 200, 500]}
                onPerPageChange={(value) => {
                  setPerPage(value);
                  setPage(1); // Reset to first page when page size changes
                }}
                onColumnFilter={handleColumnFilter}
                onClearColumnFilter={handleClearColumnFilter}
                columnFilters={columnFilters}
              />

              {/* Tags Menu */}
              <ContactsFilterPopover
                allTags={allTagsFromData}
                anchorEl={tagsAnchorEl}
                handleClose={handleTagsMenuClose}
                filteredTags={filteredTags}
                handleSearchChange={(e: any) =>
                  setTagSearchQuery(e.target.value)
                }
                setSearchQuery={setTagSearchQuery}
                setPage={setPage}
                teamData={teamData}
                setTeamData={setTeamData}
                selectedFilter={selectedFilter}
                searchQuery={tagSearchQuery}
                options={contactSubFilterOptions}
                isTeamDataActive={isTeamDataActive}
                selectedtags={selectedTags}
                setSelectedTags={setSelectedTags}
                deletedContacts={deletedContacts}
                setDeletedContacts={setDeletedContacts}
                checkedIds={checkedIds}
                setCheckedIds={setCheckedIds}
                setIsTableLoading={setIsTableLoading}
              />

              <MoveToTagsPopOver
                anchorEl={anchorelMove}
                selectedContactIds={selectedContactIds}
                setSelectedContactIds={setSelectedContactIds}
                handleSearchChange={(e: any) =>
                  setTagSearchQuery(e.target.value)
                }
                handleClose={handleCloseMove}
                filteredTags={filteredTags}
              />
            </>
          ) : (
            <>
              <CommonTable
                columns={deletedContactsColumns}
                rowIdKey="contactId"
                isLoading={isTableLoading || isFilterLoading || contactsLoading || deletedContactsLoading}
                data={deletedContacts}
                title="Contacts"
                count={allDeletedContactsData?.total}
                selectable={true}
                actions={renderActionsForDeletedContacts}
                page={page}
                onPageChange={handleChangePage}
                totalPages={Math.ceil(allDeletedContactsData?.total / perPage)}
                selectedMainFilter={selectedContactFilter}
                handleMainFilter={handleContactsAllFilter}
                renderOnMobile={renderDeletedContactsInMobile}
                searchProps={{
                  value: searchContactsQuery,
                  onChange: setSearchContactsQuery,
                  placeholder: "Search Deleted contacts...",
                }}
                selectedItemsToolbarActions={
                  selectedItemsToolbarActionsForDeletedContacts
                }
                tagsProps={{
                  onTagsClick: handleTagsMenuOpen,
                  tagsAnchorEl: tagsAnchorEl,
                  onTagsClose: handleTagsMenuClose,
                  tagsList: allTagsFromData,
                  show: true,
                }}
                selectedItemIds={selectedContactIds}
                setSelectedItemIds={setSelectedContactIds}
                onRestore={handleRestoreById}
                perPage={perPage}
                perPageOptions={[10, 25, 50, 100, 200, 500]}
                onPerPageChange={(value) => {
                  setPerPage(value);
                  setPage(1);
                }}
                // Remove column filter props for archived contacts
              />
              <ContactsFilterPopover
                allTags={allTagsFromData}
                anchorEl={tagsAnchorEl}
                handleClose={handleTagsMenuClose}
                filteredTags={filteredTags}
                handleSearchChange={(e: any) =>
                  setTagSearchQuery(e.target.value)
                }
                setSearchQuery={setTagSearchQuery}
                setPage={setPage}
                teamData={teamData}
                setTeamData={setTeamData}
                selectedFilter={selectedFilter}
                searchQuery={tagSearchQuery}
                contactSearchQuery={searchContactsQuery}
                options={contactSubFilterOptions}
                isTeamDataActive={isTeamDataActive}
                selectedtags={selectedTags}
                setSelectedTags={setSelectedTags}
                deletedContacts={deletedContacts}
                setDeletedContacts={setDeletedContacts}
                checkedIds={checkedIds}
                setCheckedIds={setCheckedIds}
                setIsTableLoading={setIsTableLoading}
              />
            </>
          )}

          {/* Popups and Dialogs */}
          <SyncContactsBySourcesPopover
            anchorEl={syncAnchorEl}
            handleClose={closeSyncSourcesDialog}
          />
          <NewContactPopOver
            open={openDialog}
            handleClose={handleCloseDialog}
          />
          {editContact && (
            <EditPopOver
              open={openEditDialog}
              handleClose={handleCloseEditDialog}
              editContact={editContact}
              setTeamData={setTeamData}
              setPage={setPage}
              isEditContactLoading={isEditContactLoading}
              setIsEditContactLoading={setIsEditContactLoading}
            />
          )}
          {getId ? (
            <DeletePopUp
              title="Selected Contact"
              open={deleteDialog}
              handleClose={handleCloseDelete}
              handleDelete={handleDelete}
              handleLoad={isDeleteLoading}
            />
          ) : (
            <>
              <DeletePopUp
                title="Selected Contacts"
                open={deleteSelectedDialog}
                handleClose={handleCloseSelectedDelete}
                handleDelete={handleDeleteById}
                handleLoad={isDeleteLoading}
              />
            </>
          )}
          <RestorePopUp
            title="Selected Contacts"
            open={restoreSelectedDialog}
            handleClose={handleCloseSelectedRestore}
            handleRestore={handleRestoreById}
            handleLoad={isRestoreLoading}
          />
          <CustomMainFilterPopover
            anchorEl={anchorElContact}
            handleClose={() => setAnchorElContact(null)}
            mainFilterOptions={contactMainFilterOptionsWithChatAndSource}
            nestedFilterOptions={contactSubFilterOptions}
            handleOptionClick={handleAllFilterOptionClick}
            setSelectedFilter={setSelectedContactFilter}
            statusDisplayName="Status"
          />

          {dialog && (
            <ImportContactsPopUp open={dialog} handleClose={handleClosed} />
          )}
          {trackerDialog && (
            <ContactsTracterPop
              open={trackerDialog}
              handleClose={handleTrackerClosed}
            />
          )}
        </Grid>
      ) : (
        <NoAccessPage />
      )}
    </>
  );
};

export default Contacts;
