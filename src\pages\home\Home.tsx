/* global process */

import { Box, Grid, Typography } from "@mui/material";
import MainSideBar from "../../components/HomeComponents/MainSideBar";
import { useLocation, useNavigate } from "react-router-dom";
import { makeStyles } from "@mui/styles";
import ProfileMainPage from "./ProfileMainPage";
import Inbox from "../Inbox/Inbox";
import CampaignsMainPage from "./CampaignMainPage";
import Contacts from "../Contacts/Contacts";
import AutomationMainPage from "./AutomationMainPage";
import AnalyticsMainPage from "./AnalyticsMainPage";
import WalletMainPage from "./WalletMainPage";
import TemplateMainPage from "./TemplateMainPage";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import EngagetoLogoSvg from "../../assets/svgs/EngagetoLogoSvg";
import { useEffect, useState } from "react";
import { HttpTransportType, HubConnectionBuilder } from "@microsoft/signalr";
import { fetchGetAllCountries } from "../../redux/slices/ManageAccount/GetAllCountryDetails";
import { fetchAllRoles } from "../../redux/slices/ManageCompany/DisplayAllRoles";
import { fetchAllCountryCodes } from "../../redux/slices/ManageAccount/GetAllCountryCode";
import { fetchManagePermissions } from "../../redux/slices/ManagePermissions/ManagePermissionsSlice";
import { fetchUserPermission } from "../../redux/slices/ManagePermissions/GetUserPermissionSlice";
import { sumOfUnreads } from "../../utils/functions";
import { generateToken } from "../../firebase";
import { fetchManageNotifications } from "../../redux/slices/ManageNotifications/ManageNotificationsSlice";
import { getWalletAndSubscription } from "../../redux/slices/Wallet/WalletSlice";
import { bgColors } from "../../utils/bgColors";
import ProfilePaymentMethod from "../profile/Subscription/PaymentMethod";
import CommerceMainPage from "./CommerceMainPage";
import LinkComponent from "../../components/common/LinkComponent";
import IntegrationsMainPage from "./IntegrationsMainPage";
import { fetchLatestInboxContacts } from "../../redux/slices/Inbox/LatestInboxContactsSlice";
import { toastActions } from "../../utils/toastSlice";
import { checkRateLimit, useRateLimitBanner } from "../../utils/rateLimiter";
import { fetchCompanyDetails } from "../../redux/slices/ManageCompany/CompanyDetailsSlice";
import {
  getStoredRefreshToken,
  refreshAccessToken,
  getStoredTokens,
  isTokenExpired,
} from "../../utils/authUtils";
import { getInboxUnreadCount } from "../../redux/slices/Inbox/GetInboxUnreadCountSlice";

const useStyles = makeStyles({
  container: {
    overflowY: "hidden",
    overflowX: "hidden",
  },
  sideBar: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "110px",
    minWidth: "110px",
    overflowY: "auto",
  },
  badge: {
    position: "sticky",
    top: 0,
    zIndex: 1000,
    width: "100%",
    color: "#fff",
    textAlign: "center",
    padding: "8px 0",
    fontSize: "14px",
  },
});

export interface ContactData {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  itemsPerPage: number;
  data: any[];
}

export interface LoadingStates {
  allContactsList: boolean;
  paginatedContactsList: boolean;
}
const WEBHOOKS_SOCKET_URL = process.env.REACT_APP_API_URL_CONVERSATION;

const Home = () => {
  const classes = useStyles();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const loginData = useAppSelector((state: any) => state.adminLogin.data);

  const tenantId = useAppSelector((state: any) => state.adminLogin.tenantId);
  const isMainSidebarAccessible = useAppSelector(
    (state: any) => state.adminLogin.isMainSidebarAccessible
  );

  const businessId = loginData?.companyId;
  const roleId = loginData?.roleId;
  const getuserPermissionSlice = useAppSelector(
    (state: any) => state.getUserPermissions
  );
  const accountDataSlice = useAppSelector((state: any) => state?.accountData);
  const accountData = accountDataSlice?.data;
  const currentPlanDetails = useAppSelector(
    (state: any) => state?.wallet?.walletAndSubscription?.data
  );
  const walletAndSubscriptionStatus = useAppSelector(
    (state: any) => state?.wallet?.walletAndSubscriptionStatus
  );
  const notifications = useAppSelector(
    (state: any) => state?.manageNotifications?.data
  );
  const chatNotification = notifications?.find(
    (menu: any) => menu.mainMenu === "Chat Notifications"
  );
  const subMenuStatus = chatNotification?.subMenus.find(
    (subMenu: any) => subMenu.name === "Get notification when i get a message"
  )?.status;
  const createContactSlice = useAppSelector(
    (state: any) => state?.createContactData?.data
  );
  const deleteContactSlice = useAppSelector(
    (state: any) => state?.removeContactData?.data
  );
  const updateContactSlice = useAppSelector(
    (state: any) => state?.updateContactData?.data
  );
  const inboxContactAssignmentSlice = useAppSelector(
    (state: any) => state?.inboxContactAssignment?.data
  );

  const [connection, setConnection] = useState<any>(null);

  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [contactsListLoading, setContactsListLoading] = useState<LoadingStates>(
    { allContactsList: false, paginatedContactsList: false }
  );
  const [contacts, setContacts] = useState<ContactData>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    itemsPerPage: 0,
    data: [],
  });
  const [messages, setMessages] = useState<any>();
  const [chat, setChat] = useState<any>();
  const [searchInput, setSearchInput] = useState<string>("");
  const [contactNumber, setContactNumber] = useState<any>("help");
  const [filterData, setFilterData] = useState<any>({ filters: [] });
  const [dateRangeFilter, setDateRangeFilter] = useState<any>({
    startDate: "",
    endDate: "",
    column: "CreatedDate",
  });

  // Reset filters when navigating to other modules
  useEffect(() => {
    // Reset all filters when location changes (navigating away from inbox)
    if (location.pathname !== "/home/<USER>") {
      setDateRangeFilter({
        startDate: "",
        endDate: "",
        column: "CreatedDate",
      });
      setSearchInput("");
      setFilterData({ filters: [] });
      setChatsPageNumber(1);
    }
  }, [location.pathname]);
  const [chatsPageNumber, setChatsPageNumber] = useState(1);
  const [notify, setNotify] = useState<boolean>(true);
  const [totalUnreads, setTotalUnreads] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);

  // Rate limiting state for SignalR calls (now managed by rateLimiter utility)
  const { isVisible: rateLimitBannerVisible, blockInfo } = useRateLimitBanner();
  const isProfileRoute = location.pathname.startsWith("/profile/");
  const isInbox = location.pathname.startsWith("/inbox");
  const isCampaingns = location.pathname.startsWith("/campaigns/");
  const isTemplate = location.pathname.startsWith("/templates");
  const isContacts = location.pathname.startsWith("/contacts");
  const isAutomation = location.pathname.startsWith("/automation/");
  const isAnalytics = location.pathname.startsWith("/analytics");
  const isWallet =
    location.pathname.startsWith("/wallet") ||
    location.pathname.startsWith("/billing-details");
  const isWalletPayment = location.pathname.startsWith("/payment");
  const isCommerce =
    location.pathname.startsWith("/commerce-settings") ||
    location.pathname.startsWith("/order-panel") ||
    location.pathname.startsWith("/catalog") ||
    location.pathname.startsWith("/auto-checkout-flow");
  const isIntegrations =
    location.pathname.startsWith("/integrations-discover") ||
    location.pathname.startsWith("/whatsapp-widget") ||
    location.pathname.startsWith("/whatsapp-link");

  const handleWebSocketMessage = (receivedMessage: any) => {
    // Check if receivedMessage status is sent, delivered, or seen
    if (
      ["sending", "sent", "delivered", "seen"].includes(receivedMessage.status)
    ) {
      // Find and remove the matching message from messages array
      setMessages((prevMessages: any) =>
        prevMessages.filter((msg: any) => {
          if (msg.mediaMimeType && receivedMessage.mediaMimeType) {
            // For media messages, compare using mediaUrl
            return !(
              msg.mediaMimeType === receivedMessage.mediaMimeType &&
              msg.status === "sending"
            );
          } else {
            // For text messages, compare using textMessage
            return !(
              msg.textMessage === receivedMessage.textMessage &&
              msg.status === "sending"
            );
          }
        })
      );
    }
  };

  // Build date range filters array
  const dateRangeFilters = [];
  if (
    dateRangeFilter?.startDate &&
    dateRangeFilter?.endDate &&
    dateRangeFilter?.column
  ) {
    dateRangeFilters.push({
      column: dateRangeFilter.column,
      fromDate: `${dateRangeFilter.startDate}T00:00:00Z`,
      toDate: `${dateRangeFilter.endDate}T00:00:00Z`,
    });
  }

  const operations = {
    searching: {
      value: searchInput,
    },
    sorting: filterData?.sort,
    filtering: {
      filterType: "and",
      conditions: filterData?.filters,
    },
    dateRangeFilters: dateRangeFilters,
  };

  const handleLoadMore = (pageNumber: number) => {
    if (loginData?.userId) {
      setPageNumber(pageNumber);
    }
  };

  useEffect(() => {
    // total unread count of all contacts
    const fetchUnreadCount = async () => {
      const response = await dispatch(getInboxUnreadCount({ businessId }));
      const data = response.payload;
      const totalUnreadCount = data;
      setTotalUnreads(totalUnreadCount);
    };
    fetchUnreadCount();
  }, [loginData?.userId, businessId, dispatch]);

  useEffect(() => {
    if (loginData?.userId && subMenuStatus !== undefined) {
      setNotify(subMenuStatus);
    }
  }, [subMenuStatus]);

  useEffect(() => {
    if (loginData?.userId) {
      const hubConnection: any = new HubConnectionBuilder()
        .withUrl(`${WEBHOOKS_SOCKET_URL}/conversations`, {
          accessTokenFactory: async () => {
            try {
              const tokens = getStoredTokens();
              if (!tokens?.token) {
                throw new Error("No token available");
              }

              // Check if token is expired
              if (isTokenExpired(tokens.token)) {
                const refreshToken = getStoredRefreshToken();
                if (!refreshToken) {
                  throw new Error("No refresh token available");
                }

                const data = {
                  token: tokens.token,
                  refreshToken: refreshToken,
                };

                const newTokens = await refreshAccessToken(data);
                if (!newTokens?.token) {
                  throw new Error("Failed to refresh token");
                }
                return newTokens.token;
              }
              return tokens.token;
            } catch (error) {
              console.error("[SignalR] Token refresh error:", error);
              throw error;
            }
          },
          transport:
            HttpTransportType.WebSockets |
            HttpTransportType.ServerSentEvents |
            HttpTransportType.LongPolling,
        })
        .withAutomaticReconnect()
        .build();

      setConnection(hubConnection);

      // Function to start connection with retry logic
      const startConnectionWithRetry = async (retryAttempt = 0) => {
        const maxRetries = 3;

        try {
          await hubConnection.start();

          setIsConnected(true);
          hubConnection?.invoke(
            "SendUserData",
            loginData?.userId,
            businessId,
            operations,
            chatsPageNumber
          );
        } catch (err) {
          console.error("[SignalR] Connection error:", err);
          setIsConnected(false);

          if (retryAttempt < maxRetries - 1) {
            // Wait for a second before retrying
            setTimeout(() => {
              startConnectionWithRetry(retryAttempt + 1);
            }, 1000);
          } else {
            console.error(
              `[SignalR] Failed to establish connection after ${maxRetries} attempts`
            );
          }
        }
      };

      // Start the connection with retry logic
      startConnectionWithRetry();

      hubConnection.onreconnecting((error: any) => {});

      hubConnection.onreconnected((connectionId: any) => {
        hubConnection.invoke(
          "SendUserData",
          loginData?.userId,
          businessId,
          operations,
          chatsPageNumber
        );
      });

      return () => {
        if (hubConnection) {
          hubConnection.stop();
        }
      };
    }
  }, [loginData]);

  useEffect(() => {
    if (connection && isConnected && loginData?.userId) {
      connection
        .invoke(
          "ContactConversationHistory",
          contactNumber,
          businessId,
          loginData?.userId,
          pageNumber
        )
        .then(() => {})
        .catch((error: any) => {});

      connection.on("SendConversations", (data: any) => {
        if (data?.data) {
          setChat(data);

          if (pageNumber === 1) {
            // Ensure data.data is not undefined before setting messages
            setMessages(data.data);
          } else {
            // Ensure both data.data and messages are defined
            setMessages(() => [...data.data, ...messages]);
          }
        } else {
          // Handle the case where data or data.data is undefined
          // Optionally set default values or handle errors here
        }
      });
    }
  }, [
    connection,
    isConnected,
    contactNumber,
    pageNumber,
    inboxContactAssignmentSlice,
  ]);

  //send and received message can be handled here
  useEffect(() => {
    const handleNewMessage = async (data: any) => {
      // Check rate limit before processing
      if (!checkRateLimit()) {
        console.warn(
          "[Rate Limit] Message processing blocked due to rate limiting"
        );
        return;
      }

      const isContactNumberOpen =
        data[0]?.from.length < 14
          ? data[0]?.from.startsWith("+")
            ? data?.[0]?.from.slice(1)
            : data?.[0]?.from
          : data?.[0]?.to.length < 14
          ? data?.[0]?.to.startsWith("+")
            ? data?.[0]?.to.slice(1)
            : data?.[0]?.to
          : data?.[0]?.to;
      // Check if the contactNumber matches either from or to
      if (contactNumber === isContactNumberOpen) {
        handleWebSocketMessage(data[0]);
        setMessages((prevConversations: any) => {
          const index = prevConversations?.findIndex(
            (message: any) => message?.id === data[0]?.id
          );

          if (index !== -1) {
            const updatedConversations = [...prevConversations];
            updatedConversations[index] = data[0];
            return updatedConversations;
          } else {
            connection.invoke("MarkAsRead", data[0]?.id, businessId);
            return [...prevConversations, data[0]];
          }
        });
      }
      // Find the contact that received/sent the message
      const messageContact =
        data[0]?.from?.length < 13 ? data[0]?.from : data[0]?.to;

      // Update contacts list
      setContacts((prev: any) => {
        // Find if contact exists in current list
        const existingContact = prev.data.find(
          (item: any) => item.contact === messageContact
        );

        if (existingContact) {
          // If contact exists, update it and move to top
          const newContact = {
            ...existingContact,
            lastMessageAt: "00:00:00.0000000",
            lastMessage: data[0]?.textMessage || `📝 ${data[0]?.templateBody}`,
            unRead:
              messageContact === contactNumber
                ? 0
                : (existingContact.unRead || 0) + 1,
            chatStatus:
              data[0].from?.length < 13 ? "open" : existingContact.chatStatus,
          };

          // Filter out the existing contact and add updated one at top
          const filteredContacts = prev.data.filter(
            (item: any) => item.contact !== messageContact
          );

          return {
            ...prev,
            data: [newContact, ...filteredContacts],
          };
        } else {
          // If contact doesn't exist, only fetch and add new contact if user is in Inbox module
          if (isInbox) {
            dispatch(
              fetchLatestInboxContacts({
                userId: loginData?.userId,
                filtering: {
                  searching: {
                    value: messageContact,
                  },
                  sorting: {},
                  filtering: {
                    filterType: "and",
                    conditions: [],
                  },
                },
                businessId,
                chatsPageNumber: 1,
              })
            )
              .then((response) => {
                const newContactData = response.payload?.data;

                if (newContactData?.length > 0) {
                  setContacts((currentPrev: any) => {
                    // Double check the contact hasn't been added while we were fetching
                    const contactStillNotExists = !currentPrev.data.find(
                      (item: any) => item.contact === messageContact
                    );

                    if (contactStillNotExists) {
                      // Create new contact with updated last message properties
                      const newContact = {
                        ...newContactData[0],
                        lastMessageAt: "00:00:00.0000000",
                        lastMessage:
                          data[0]?.textMessage || `📝 ${data[0]?.templateBody}`,
                        unRead: data[0]?.from ? 1 : 0,
                        chatStatus: "open",
                      };

                      const updatedData = [newContact, ...currentPrev.data];
                      const trimmedData = updatedData.slice(0, 30);

                      return {
                        ...currentPrev,
                        data: trimmedData,
                      };
                    }
                    return currentPrev;
                  });
                }
              })
              .catch((err) => {
                console.error("Error fetching new contact:", err);
                dispatch(
                  toastActions.setToaster({
                    message: "Error loading new contact",
                    type: "Error",
                  })
                );
              });
          }

          return prev; // Return previous state while waiting for API response
        }
      });
      setTotalUnreads((prev) => {
        if (data[0]?.from?.length < 13 && data[0]?.from !== contactNumber) {
          return prev + 1;
        }
        return prev;
      });
      if (data[0]?.from?.length < 14 && notify) {
        if (
          navigator.serviceWorker &&
          navigator.serviceWorker.controller &&
          document.visibilityState === "hidden"
        ) {
          navigator.serviceWorker.controller.postMessage({
            type: "NEW_MESSAGE",
            message: {
              title: `${data[0]?.from}`,
              body: `${data[0]?.textMessage || data[0]?.mediaMimeType}`, // Customize the body content
              icon: "/images/profile.png", // Specify an appropriate icon
              data: {
                url: `/inbox/${data[0]?.from}`,
                contactNumber: data[0]?.from,
              },
            },
          });
        } else if (
          navigator.serviceWorker &&
          navigator.serviceWorker.controller &&
          data[0].from !== contactNumber
        ) {
          navigator.serviceWorker.controller.postMessage({
            type: "NEW_MESSAGE",
            message: {
              title: `${data[0]?.from}`,
              body: `${data[0]?.textMessage || data[0]?.mediaMimeType}`, // Customize the body content
              icon: "/images/profile.png", // Specify an appropriate icon
              data: {
                url: `/inbox/${data[0]?.from}`,
                contactNumber: data[0]?.from,
              },
            },
          });
        } else if (
          navigator.serviceWorker &&
          navigator.serviceWorker.controller &&
          !isInbox
        ) {
          navigator.serviceWorker.controller.postMessage({
            type: "NEW_MESSAGE",
            message: {
              title: `${data[0]?.from}`,
              body: `${data[0]?.textMessage || data[0]?.mediaMimeType}`, // Customize the body content
              icon: "/images/profile.png", // Specify an appropriate icon
              data: {
                url: `/inbox/${data[0]?.from}`,
                contactNumber: data[0]?.from,
              },
            },
          });
        }
      }
    };

    if (connection && isConnected && loginData?.userId) {
      connection.on("ReceiveMessageFromServer", handleNewMessage);
    }

    return () => {
      if (connection) {
        connection.off("ReceiveMessageFromServer", handleNewMessage);
        // connection.off("SendConversations");
      }
    };
  }, [connection, isConnected, contactNumber, notify]);

  useEffect(() => {
    try {
      if (connection && isConnected && loginData?.userId) {
        // const handleErrorMessage = async (data: any) => {

        //   // Additional error handling logic can go here
        // };

        connection.on("ReceiveErrorMessageFromServer", (data: any) => {
          setMessages((prevConversations: any) => {
            // Check if the message already exists in the messages state
            const index = prevConversations?.findIndex(
              (message: any) => message?.id === data[0]?.id
            );

            // If the message doesn't exist, mark as read and append it
            if (index !== -1) {
              // If the message is found, replace it
              const updatedConversations = [...prevConversations];
              updatedConversations[index] = data[0];
              return updatedConversations;
            }
            return prevConversations;
          });
        });

        // Cleanup on unmount or when dependencies change
        return () => {
          connection.off("ReceiveErrorMessageFromServer", () => {});
        };
      } else {
      }
    } catch (error) {}
  }, [connection, isConnected, loginData?.userId]);

  const handleLatestInboxContacts = async () => {
    try {
      const response = await dispatch(
        fetchLatestInboxContacts({
          userId: loginData?.userId,
          filtering: operations,
          businessId,
          chatsPageNumber,
        })
      );
      const data = response.payload;
      if (data?.data) {
        if (chatsPageNumber === 1) {
          setContacts(() => ({
            currentPage: data.currentPage,
            totalPages: data.totalPages,
            totalCount: data.totalCount,
            itemsPerPage: data.pageSize,
            data: data.data,
          }));
        } else {
          setContacts((prevContacts) => ({
            currentPage: data.currentPage,
            totalPages: data.totalPages,
            totalCount: data.totalCount,
            itemsPerPage: data.pageSize,
            data: [...prevContacts.data, ...data.data],
          }));
        }
      }
    } catch (err) {
      dispatch(
        toastActions.setToaster({
          message: "Error loading Contacts",
          type: "Error",
        })
      );
    } finally {
      setContactsListLoading({
        allContactsList: false,
        paginatedContactsList: false,
      });
    }
  };

  useEffect(() => {
    if (loginData?.userId || tenantId) {
      if (chatsPageNumber === 1) {
        setContactsListLoading({
          allContactsList: true,
          paginatedContactsList: false,
        });
      }
      console.log("handleLatestInboxContacts called due to dependency change");
      console.log("Dependencies:", {
        "filterData?.filters": filterData?.filters,
        "filterData?.sort": filterData?.sort,
        dateRangeFilter: dateRangeFilter,
        chatsPageNumber: chatsPageNumber,
        searchInput: searchInput,
        tenantId: tenantId,
      });

      // Debounce function
      const handler = setTimeout(() => {
        handleLatestInboxContacts();
      }, 500);

      return () => clearTimeout(handler);
    }
  }, [
    filterData?.filters,
    filterData?.sort,
    dateRangeFilter,
    chatsPageNumber,
    searchInput,
    tenantId,
  ]);

  useEffect(() => {
    if (loginData?.userId) {
      setPageNumber(1);
    }
  }, [contactNumber]);

  useEffect(() => {
    return () => {
      if (connection) {
        connection.stop();
        setContacts({
          currentPage: 1,
          totalPages: 1,
          totalCount: 0,
          itemsPerPage: 0,
          data: [],
        });
      }
    };
  }, [connection]);

  useEffect(() => {
    if ("serviceWorker" in navigator) {
      navigator.serviceWorker
        .register("/firebase-messaging-sw.js")
        .then((registration) => {
          navigator.serviceWorker.ready.then(() => {
            try {
              generateToken();
            } catch (err) {}
          });
        })
        .catch((err) => {});
    }

    // onMessage(messaging, (payload) => {

    // });
  }, []);

  useEffect(() => {
    navigator.serviceWorker.addEventListener("message", (event) => {
      if (event.data.type === "navigate-to-chat") {
        const { urlToOpen, contactNumber: number } = event.data;

        setContactNumber(number);

        navigate(`${urlToOpen}`);
      }
    });

    return () => {
      navigator.serviceWorker.removeEventListener("message", () => {});
    };
  }, []);

  useEffect(() => {
    const tokens = getStoredTokens();
    const token = tokens?.token ? `Bearer ${tokens?.token}` : "";

    if (location.pathname === "" || location.pathname === "/") {
      if (token && currentPlanDetails?.subscriptionPlan?.isActive) {
        navigate("/inbox/help");
      } else if (
        token &&
        (currentPlanDetails === undefined ||
          currentPlanDetails === null ||
          currentPlanDetails?.subscriptionPlan?.isActive === false)
      ) {
        navigate(`/profile/manage-account/${loginData?.userId}`);
      } else {
        navigate("/login");
      }
    }
  }, [loginData?.userId, location]);

  useEffect(() => {
    dispatch(fetchGetAllCountries());
    dispatch(fetchAllCountryCodes());
    dispatch(fetchAllRoles(businessId));
    dispatch(fetchCompanyDetails(businessId));
    // dispatch(fetchAccountDetails(loginData?.userId));
    // dispatch(fetchCurrentPlanDetails(businessId));
    dispatch(fetchManagePermissions({ roleId: roleId, companyId: businessId }));
    dispatch(
      fetchUserPermission({
        roleId: roleId,
        companyId: businessId,
      })
    );
    dispatch(getWalletAndSubscription(businessId));
    dispatch(
      fetchManageNotifications({
        userId: loginData?.userId,
        companyId: businessId,
      })
    );
  }, [dispatch]);

  return (
    <>
      {/* Rate Limiting Banner */}
      {rateLimitBannerVisible && (
        <Box
          className={classes.badge}
          sx={{ backgroundColor: bgColors?.yellow }}
        >
          <Typography>
            ⚠️ Message processing temporarily paused due to high activity.
            Normal operation will resume in {blockInfo.remainingMinutes}{" "}
            minutes.
          </Typography>
        </Box>
      )}

      {accountData?.companyVerificationStatus === true &&
      accountData?.isMetaEnabled === false &&
      (currentPlanDetails?.subscriptionPlan?.isActive === true ||
        currentPlanDetails?.subscriptionPlan?.isActive === false) ? (
        <Box className={classes.badge} sx={{ backgroundColor: bgColors?.red }}>
          <Typography>
            Oops!! Your account has been disabled by Meta. Please contact our
            support team.&nbsp;
            {!location?.pathname?.includes("profile/manage-account/") && (
              <LinkComponent
                title="Click here to view details"
                color={bgColors?.white}
                underline="always"
                href={`/profile/manage-account/${businessId}`}
              />
            )}
          </Typography>
        </Box>
      ) : accountData?.companyVerificationStatus === false &&
        accountData?.isMetaEnabled === false &&
        currentPlanDetails?.subscriptionPlan?.isActive === false ? (
        <Box
          className={classes.badge}
          sx={{ backgroundColor: bgColors?.yellow }}
        >
          <Typography>
            Your free trail has been expired. Please subscribe a plan to resume
            your Engageto activities.
          </Typography>
        </Box>
      ) : accountData?.companyVerificationStatus === false &&
        accountData?.isMetaEnabled === false &&
        currentPlanDetails?.subscriptionPlan?.planName !== "Intro" &&
        currentPlanDetails?.subscriptionPlan?.isActive === true ? (
        <Box
          className={classes.badge}
          sx={{ backgroundColor: bgColors?.green }}
        >
          <Typography>
            You have an active subscription. Please link with Meta to start
            engaging with your customers.
          </Typography>
        </Box>
      ) : (
        accountData?.companyVerificationStatus === true &&
        accountData?.isMetaEnabled === true &&
        currentPlanDetails?.subscriptionPlan?.isActive === false && (
          <Box
            className={classes.badge}
            sx={{ backgroundColor: bgColors?.yellow }}
          >
            <Typography>
              Your current plan has been expired. Please subscribe or upgrade to
              a new plan to resume your Engageto activities.
            </Typography>
          </Box>
        )
      )}
      <Grid
        container
        className={classes.container}
        sx={{
          position: "relative",
        }}
      >
        {accountDataSlice?.status === "loading" ||
        walletAndSubscriptionStatus === "loading" ||
        getuserPermissionSlice?.status === "loading" ? (
          <Box
            sx={{ justifyContent: "center", width: "100%", height: "100vh" }}
          >
            {/* <LoadingComponent height="100%" color={bgColors?.blue} /> */}
            <Box id="loading-screen">
              <EngagetoLogoSvg />
              {/* <img
              src="/loading2.gif"
              style={{ width: "48px", height: "20px" }}
            /> */}
            </Box>
          </Box>
        ) : (
          <>
            {isMainSidebarAccessible !== false && (
              <Grid
                item
                flexGrow={0}
                sx={{ width: { xs: "60px", md: "100px" } }}
                // className={classes.sideBar}
              >
                <MainSideBar
                  totalUnreads={totalUnreads}
                  setContactNumber={setContactNumber}
                />
              </Grid>
            )}

            <Grid
              item
              sx={{
                width: {
                  xs:
                    isMainSidebarAccessible === false
                      ? "100%"
                      : "calc(100% - 60px)",
                  md:
                    isMainSidebarAccessible === false
                      ? "100%"
                      : "calc(100% - 100px)",
                },
              }}
              flexGrow={1}
            >
              {isProfileRoute && <ProfileMainPage />}
              {isInbox && (
                <Inbox
                  contactsListLoading={contactsListLoading}
                  setContactsListLoading={setContactsListLoading}
                  connection={connection}
                  contacts={contacts}
                  setContacts={setContacts}
                  messages={messages}
                  chat={chat}
                  filterData={filterData}
                  chatsPageNumber={chatsPageNumber}
                  setChatsPageNumber={setChatsPageNumber}
                  setMessages={setMessages}
                  searchInput={searchInput}
                  setSearchInput={setSearchInput}
                  setFilterData={setFilterData}
                  setContactNumber={setContactNumber}
                  contactNumber={contactNumber}
                  handleLoadMore={handleLoadMore}
                  handleLatestInboxContacts={handleLatestInboxContacts}
                  setTotalUnreads={setTotalUnreads}
                  dateRangeFilter={dateRangeFilter}
                  setDateRangeFilter={setDateRangeFilter}
                />
              )}
              {isCampaingns && <CampaignsMainPage />}
              {isContacts && <Contacts />}
              {isAutomation && <AutomationMainPage />}
              {/* {isAnalytics && <AnalyticsMainPage />} */}
              {isWallet && <WalletMainPage />}
              {isWalletPayment && <ProfilePaymentMethod />}
              {/* {isWalletCardDetails && <ProfileNewCard />} */}
              {isTemplate && <TemplateMainPage />}
              {isCommerce && <CommerceMainPage />}
              {isIntegrations && <IntegrationsMainPage />}
              {/* {isWhatsappLink && <WhatsappLink />} */}
            </Grid>
          </>
        )}
      </Grid>
    </>
  );
};

export default Home;
