import {
  Box,
  Card,
  CardContent,
  Button,
  Chip,
  CircularProgress,
  Grid,
  IconButton,
  Popover,
  Tooltip,
  Typography,
} from "@mui/material";
import FileDownloadOutlinedIcon from "@mui/icons-material/FileDownloadOutlined";
import { makeStyles } from "@mui/styles";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { bgColors } from "../../utils/bgColors";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { getCampaign } from "../../redux/slices/Campaign/GetCampaignSlice";
import LoadingComponent from "../../components/common/LoadingComponent";
import EditCampaign from "../../components/ScheduledComponents/EditCampaign";
import { checkOnetimeCampaignsPermission } from "../../utils/permissions";
import NoAccessPage from "../../components/common/NoAccess";
import { toastActions } from "../../utils/toastSlice";
import { CAMPAIGN_API } from "../../Apis/Campaign/Campaign";
import CommonTable, { TableColumn } from "../../components/common/CommonTable";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import CloseSvg from "../../assets/svgs/CloseSvg";
import { Add } from "@mui/icons-material";
import { debounce } from "lodash";
import ResendCampaignPopup from "../../components/ScheduledComponents/ResendCampaignPopup";
import { Sync as SyncIcon } from "@mui/icons-material";
import WalletBalanceDisplay from "../../components/common/WalletBalanceDisplay";
import { useNavigate } from "react-router-dom";
import { DateRange } from "../../components/common/DateRangeFilterPopover";
import moment from "moment";
import { campaignAllFilters, campaignAllFiltersActions } from "../../redux/slices/Campaign/CampaignAllFiltersSlice";
import { campaignStateOptions } from "../../utils/constants";

interface CreateCampaignState {
  businessId: string;
  name: string;
  audiences: any[];
  scheduleDate: string | null;
  template: {
    id: string;
    mediaType: any;
    bodyValues: { value: string; fallbackValue: string }[] | null;
    headerValue: { value: string; fallbackValue: string } | null;
  };
  text: string;
  mediaUrl: string;
}

export const CampaignStatusEnum = {
  Completed: 1,
  Incompleted: 2,
  Scheduled: 3,
  Processing: 4,
};

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white1,

    height: "100vh",
    width: "100%",
    overFlow: "hidden !important",
  },
  searchField: {
    width: "100%",
    borderRadius: "12px",
    // height: "38px",
    // backgroundColor: bgColors.white2,
    backgroundColor: "white",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "inter",
      color: "#000000 !important",
    },
  },
  bgContainer: {
    backgroundColor: bgColors.white,
    height: "100%",
    width: "100%",
    overflow: "hidden !important",
    display: "flex",
    flexDirection: "column",
  },
  manageTeamContainer: {
    display: "flex",
    marginTop: "10px",
    width: "full",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
  },
  SaveChangesButton: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    width: "120px",
    height: "32px",
    cursor: "pointer",
  },
  messageCountContainer: {
    // border: "1px solid #F2F2F2",
    // borderRadius: "6px",
    // padding: "8px",
    // width: "30px",
    // paddingBottom: 6,
  },
  messageInnerContainer: {
    border: "2px solid #F2F2F2",
    borderRadius: "6px",
    paddingInline: "4px",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  grayColor: {
    color: "#303030",
    opacity: "60%",
    fontSize: "20px",
  },
  blur: {
    filter: "blur(5px)",
    opacity: 0.5,
    transition: "filter 0.3s ease-in-out, opacity 0.3s ease-in-out",
  },
  clear: {
    filter: "none",
    opacity: 1,
    transition: "filter 0.3s ease-in-out, opacity 0.3s ease-in-out",
  },
  spaceBetween: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  iconStyles: {
    cursor: "pointer",
    paddingLeft: "5px",
    alignItems: "center",
    color: "#ffffff",
  },
  table: {
    minWidth: "950px",
    overflow: "auto",
    borderCollapse: "separate",
    borderSpacing: "0",
    textAlign: "center",
    borderColor: "lightgray",
    "& th, & td": {
      borderBottom: "1px solid #f0f0f0",
      height: "35.8px",
    },
    "& th:first-child, & td:first-child": {
      borderLeft: "none",
    },
    "& th:last-child, & td:last-child": {
      borderRight: "none",
    },
  },
  teamProfileContainer: {
    display: "flex",
    alignItems: "center",
  },
});

const Campaigns = () => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const state = useAppSelector((state: any) => state);
  const campaignsPermissionsArray =
    state?.getUserPermissions?.data?.campaigns || [];
  const campaignAllFiltersData =
    state?.campaignAllFiltersData?.data?.data || [];
  const campaignAllFiltersStatus =
    state?.campaignAllFiltersData?.status || "idle";
  const getCampaignAnalyticsStatus = state?.getCampaign?.status || "";
  // Removed: const campaignsCount = state?.getCampaignCount?.data?.data || 0;

  const getCampaignAnalyticsDetailsStatus =
    state?.getCampaignAnalyticsDetailsData?.status || "";
  const oneTimeCampaignsPermissionsObject = campaignsPermissionsArray?.find(
    (item: any) => Object?.prototype?.hasOwnProperty?.call(item, "oneTime")
  );

  const oneTimeCampaignsPermissionsActions = oneTimeCampaignsPermissionsObject
    ? oneTimeCampaignsPermissionsObject.oneTime
    : [];
  const hasOnetimeCampaignsPermission = checkOnetimeCampaignsPermission(
    campaignsPermissionsArray
  );

  const [addNewCampaignTooltip, setAddNewCampaignTooltip] = useState(false);

  const [searchCampaignQuery, setSearchCampaignQuery] = useState<string>("");

  const [page, setPage] = useState(1);
  const [openDialog, setOpenDialog] = React.useState(false);
  const [pageData, setPageData] = useState([]);
  // Add a state for totalCount
  const [totalCount, setTotalCount] = useState(0);
  const [isCampaignSaved, setIsCampaignSaved] = useState(false);

  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const getMc = getuserPermissionData?.campaigns;
  const scheduledObject = getMc?.find((item: any) =>
    Object?.prototype?.hasOwnProperty?.call(item, "scheduled")
  );
  const userInfoSlice = useAppSelector((state: any) => state.adminLogin);
  const accountInfo = useAppSelector((state: any) => state?.accountData?.data);

  const userInfo = userInfoSlice?.data;
  const scheduledActions = scheduledObject ? scheduledObject.scheduled : [];

  const [createCampaignState, setCreateCampaignState] =
    useState<CreateCampaignState>({
      businessId: "",
      name: "",
      audiences: [],
      scheduleDate: "",
      template: {
        id: "",
        mediaType: null,
        bodyValues: null,
        headerValue: null,
      },
      text: "",
      mediaUrl: "",
    });

  const [perPage, setPerPage] = useState(10);

  // Column filter states
  const [columnFilters, setColumnFilters] = useState<Record<string, string>>(
    {}
  );
  const [dateRangeFilter, setDateRangeFilter] = useState<DateRange>({
    startDate: "",
    endDate: "",
  });
  const [isFilterLoading, setIsFilterLoading] = useState(false);
  const [columnFilterLoading, setColumnFilterLoading] = useState<
    Record<string, boolean>
  >({});

  const navigate = useNavigate();

  // Clear data when component mounts to ensure fresh loading state
  useEffect(() => {
    dispatch(campaignAllFiltersActions.clearData());
    
    // Clear data when component unmounts to prevent data leakage
    return () => {
      dispatch(campaignAllFiltersActions.clearData());
    };
  }, [dispatch]);

  const hasAccess = (permission: any) => {
    if (oneTimeCampaignsPermissionsActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  const hasAcess = (permission: any) => {
    if (scheduledActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  const handleColumnFilter = (columnId: string, selectedValue: string) => {
    // Set loading for this specific column
    setColumnFilterLoading((prev) => ({
      ...prev,
      [columnId]: true,
    }));
    setColumnFilters((prev) => ({
      ...prev,
      [columnId]: selectedValue,
    }));
    setPage(1);
  };

  const handleClearColumnFilter = (columnId: string) => {
    // Set loading for this specific column
    setColumnFilterLoading((prev) => ({
      ...prev,
      [columnId]: true,
    }));
    setColumnFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[columnId];
      return newFilters;
    });
    setPage(1);
  };

  const handleOpenDialog = () => {
    const hasPermissionToCreateCampaign = hasAccess("newCampaign");
    if (hasPermissionToCreateCampaign) {
      setOpenDialog(true);
    } else {
      setAddNewCampaignTooltip(true);
      setTimeout(() => {
        setAddNewCampaignTooltip(false);
      }, 2000);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  useEffect(() => {
    setPageData(campaignAllFiltersData);
    // Set totalCount from the payload if available
    const count = state?.campaignAllFiltersData?.data?.total;
    setTotalCount(count || 0);
  }, [campaignAllFiltersData, state?.campaignAllFiltersData?.data?.total]);

  // Removed debounceFetchCampaignCount and related useEffect

  const debouncedFetchCampaignData = useCallback(
    debounce(async (data) => {
      const res = await dispatch(campaignAllFilters(data));
      console.log(res, "res");
    }, 500),
    []
  );

  const fetchCampaignData = useCallback(
    (pageNumber = page) => {
      // Build filter conditions based on columnFilters
      const filterConditions = [];

      // Add state condition if any values are selected
      if (columnFilters.state) {
        filterConditions.push({
          column: "State",
          operator: "equals",
          value: columnFilters.state,
          // logicalOperator: "and",
        });
      }

      // Build date range filters
      const dateRangeFilters = [];
      if (columnFilters.createdDate) {
        try {
          const dateRange = JSON.parse(columnFilters.createdDate);
          if (dateRange.startDate && dateRange.endDate) {
            dateRangeFilters.push({
              column: "CreatedDate",
              fromDate: moment(`${dateRange.startDate}T00:00:00Z`)
                .utc()
                .format(),
              toDate: moment(`${dateRange.endDate}T00:00:00Z`).utc().format(),
            });
          }
        } catch {}
      }
      if (columnFilters.dateSetLive) {
        try {
          const dateRange = JSON.parse(columnFilters.dateSetLive);
          if (dateRange.startDate && dateRange.endDate) {
            dateRangeFilters.push({
              column: "DateSetLive",
              fromDate: moment(`${dateRange.startDate}T00:00:00Z`)
                .utc()
                .format(),
              toDate: moment(`${dateRange.endDate}T00:00:00Z`).utc().format(),
            });
          }
        } catch {}
      }

      // Only the filter/search/sort/dateRange objects in the body
      const filters = {
        searching: {
          value: searchCampaignQuery?.length > 0 ? searchCampaignQuery : "",
        },
        sorting: {
          column: "",
          order: "",
        },
        filtering: {
          filterType: filterConditions.length > 0 ? "and" : "and",
          conditions: filterConditions, // Only state filters, no default dateSetLive
        },
        dateRangeFilters: dateRangeFilters,
      };
      debouncedFetchCampaignData({
        filters,
        page: pageNumber,
        perPage,
        businessId: userInfo?.companyId,
        userId: userInfo?.userId,
      });
    },
    [searchCampaignQuery, perPage, columnFilters, debouncedFetchCampaignData]
  );

  useEffect(() => {
    fetchCampaignData(page);
  }, [searchCampaignQuery, page, columnFilters, fetchCampaignData]);

  // Clear column filter loading states when API call completes
  useEffect(() => {
    if (
      campaignAllFiltersStatus === "succeeded" ||
      campaignAllFiltersStatus === "failed"
    ) {
      console.log("API call completed, clearing loading states");
      setColumnFilterLoading({});
      setIsFilterLoading(false);
    }
  }, [campaignAllFiltersStatus]);

  // Set loading state when API call starts
  useEffect(() => {
    if (campaignAllFiltersStatus === "loading") {
      console.log("API call started, setting loading state");
      setIsFilterLoading(true);
    }
  }, [campaignAllFiltersStatus]);

  const formatDate = (datetime: any) => {
    const date = new Date(datetime);
    // Convert UTC to IST by adding 5 hours and 30 minutes
    date.setHours(date.getHours() + 5);
    date.setMinutes(date.getMinutes() + 30);

    const year = date.getFullYear();
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[date.getMonth()];
    const day = ("0" + date.getDate()).slice(-2);
    const hours = ("0" + date.getHours()).slice(-2);
    const minutes = ("0" + date.getMinutes()).slice(-2);
    const seconds = ("0" + date.getSeconds()).slice(-2);
    return `${day} ${month} ${year} ${hours}:${minutes}:${seconds}`;
  };

  const handlePageChange = (event: any, value: number) => {
    setPage(value);
  };

  // Remove the Action column from the table and make each row clickable
  const columns: TableColumn[] = [
    {
      id: "campaignTitle",
      label: "Title",
      format: (value) => (
        <Tooltip title={value} placement="top">
          <Box
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              maxWidth: "250px",
            }}
          >
            {value}
          </Box>
        </Tooltip>
      ),
    },
    {
      id: "createdby",
      label: "Created by",
    },
    {
      id: "state",
      label: "State",
      filterable: true,
      filterOptions: campaignStateOptions
        .filter((item) => item.id !== "3")
        .map((item) => ({
          id: item.id,
          value: item.value,
        })),
      format: (value) => {
        const stateMap = {
          1: { label: "Completed", color: "success" },
          2: { label: "Incomplete", color: "error" },
          3: { label: "Scheduled", color: "warning" },
          4: { label: "Processing", color: "info" },
        };
        const state = stateMap[value as keyof typeof stateMap];
        return (
          <Chip
            label={state?.label || ""}
            color={state?.color as any}
            size="small"
            sx={{
              background:
                stateMap[value as keyof typeof stateMap]?.color === "success"
                  ? "#D1FADF"
                  : stateMap[value as keyof typeof stateMap]?.color === "error"
                  ? "#FEF2F2"
                  : "#FFFBEB",
              color:
                stateMap[value as keyof typeof stateMap]?.color === "success"
                  ? "#039855"
                  : stateMap[value as keyof typeof stateMap]?.color === "error"
                  ? "#DC2626"
                  : "#DC2626",
            }}
          />
        );
      },
    },
    {
      id: "createdDate",
      label: "Created Date",
      filterable: true,
      filterOptions: [], // Will be handled by date filter
      format: (value) => formatDate(value),
    },
    {
      id: "dateSetLive",
      label: "Date Set Live",
      filterable: true,
      filterOptions: [], // Will be handled by date filter
      format: (value) => formatDate(value),
    },
  ];

  // Update handleRowClick to navigate to the dashboard page
  const handleRowClick = (row: any) => {
    navigate(`/campaigns/one-time/${row.campaignId}`);
  };

  const renderCampaignMobile = (row: any) => {
    return (
      <Card
        onClick={() => handleRowClick(row)}
        sx={{ m: 1, p: 2, cursor: "pointer" }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            flexWrap: "wrap",
          }}
        >
          <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
            {row.campaignTitle}
          </Typography>
          <Chip
            label={
              row.state === 1
                ? "Completed"
                : row.state === 2
                ? "Incomplete"
                : row.state === 3
                ? "Scheduled"
                : row.state === 4
                ? "Processing"
                : "Scheduled"
            }
            color={
              row.state === 1
                ? "success"
                : row.state === 2
                ? "error"
                : row.state === 3
                ? "warning"
                : row.state === 4
                ? "info"
                : "warning"
            }
            size="small"
          />
        </Box>
        <CardContent sx={{ p: 0, "&:last-child": { paddingBottom: 0 } }}>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="caption" color="text.secondary">
                Created by
              </Typography>
              <Typography variant="body2">{row.createdby}</Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="caption" color="text.secondary">
                Date Set Live
              </Typography>
              <Typography variant="body2">
                {formatDate(row.dateSetLive)}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  };

  return (
    <>
      {hasOnetimeCampaignsPermission ? (
        <Grid className={classes.mainContainer}>
          <Box className={classes.bgContainer}>
            <CommonTable
              columns={columns}
              data={pageData}
              rowIdKey="campaignId"
              title="Campaigns"
              count={totalCount}
              page={page}
              onPageChange={handlePageChange}
              totalPages={Math.ceil(totalCount / perPage)}
              showPagination={true}
              isLoading={
                campaignAllFiltersStatus === "loading" ||
                getCampaignAnalyticsStatus === "loading"
              }
              status={campaignAllFiltersStatus}
              renderOnMobile={renderCampaignMobile}
              searchProps={{
                value: searchCampaignQuery,
                onChange: setSearchCampaignQuery,
                placeholder: "Search campaigns...",
              }}
              primaryAction={{
                label: "Add Campaign",
                onClick: handleOpenDialog,
                icon: <Add />,
                show: hasAccess("newCampaign"),
              }}
              perPage={perPage}
              perPageOptions={[10, 25, 50, 100, 200, 500]}
              onPerPageChange={(value) => {
                setPerPage(value);
                setPage(1);
              }}
              onRowClick={(row) => {
                if (row.state !== 4) {
                  handleRowClick(row);
                }
              }}
              onColumnFilter={handleColumnFilter}
              onClearColumnFilter={handleClearColumnFilter}
              columnFilters={columnFilters}
              columnFilterLoading={columnFilterLoading}
            />
          </Box>
          <EditCampaign
            title="Add"
            data={null}
            open={openDialog}
            handleClose={handleCloseDialog}
            isCampaignSaved={isCampaignSaved}
            setIsCampaignSaved={setIsCampaignSaved}
            createCampaignState={createCampaignState}
            setCreateCampaignState={setCreateCampaignState}
            searchCampaignQuery={searchCampaignQuery}
          />
        </Grid>
      ) : (
        <NoAccessPage />
      )}
    </>
  );
};

export default Campaigns;
