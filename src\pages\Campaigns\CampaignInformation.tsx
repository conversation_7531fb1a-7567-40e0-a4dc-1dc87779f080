import React, { useState } from "react";
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  CircularProgress,
} from "@mui/material";
import PeopleAltIcon from "@mui/icons-material/PeopleAlt";
import EmailIcon from "@mui/icons-material/Email";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import CommonButton from "../../components/common/CommonButton";
import FileDownloadOutlinedIcon from "@mui/icons-material/FileDownloadOutlined";
import ResendCampaignPopup from "../../components/ScheduledComponents/ResendCampaignPopup";
import DevicePreviewComponent from "../../components/TemplateComponents/TemplateForm/devicePreview";
import { toastActions } from "../../utils/toastSlice";
import { useAppDispatch } from "../../utils/redux-hooks";
import { CAMPAIGN_API } from "../../Apis/Campaign/Campaign";
import LoadingComponent from "../../components/common/LoadingComponent";
import { bgColors } from "../../utils/bgColors";

// Add date formatting utility
function formatDateDMY(dateString: string) {
  if (!dateString) return "";
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

const CampaignInformation = ({
  campaignDetails,
  templateDetails,
  campaignAnalyticsDetails,
  isLoading,
  refreshCampaignData,
}: any) => {
  const [openResendDialog, setOpenResendDialog] = React.useState(false);
  const [rerunType, setRerunType] = React.useState<
    "failed" | "undelivered" | "both"
  >("failed");
  const [downloadingReport, setDownloadingReport] = useState<string | null>(
    null
  );
  const dispatch = useAppDispatch();
  const totalContacts = campaignAnalyticsDetails?.attemptedCount || 0;
  const failedContacts = campaignAnalyticsDetails?.failedCount || 0;
  const undeliveredContacts = campaignAnalyticsDetails?.undelivered || 0;

  const handleDownloadReport = async (row: any) => {
    setDownloadingReport(row.campaignId); // Set loading state for specific row
    try {
      const response = await CAMPAIGN_API.getCampaignReport({
        campaignId: row.campaignId,
      });

      const data = response?.data;

      if (data) {
        const blob = new Blob([data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `Report_${row.campaignTitle}.xlsx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        dispatch(
          toastActions.setToaster({
            type: "success",
            message: "Successfully downloaded the Excel file.",
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: "Unable to download the Excel file.",
          })
        );
      }
    } catch (error) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Error downloading the report.",
        })
      );
    } finally {
      setDownloadingReport(null); // Reset loading state
    }
  };

  const handleOpenResendDialog = () => {
    setOpenResendDialog(true);
  };
  const handleCloseResendDialog = () => {
    setOpenResendDialog(false);
  };

  if (isLoading) {
    return <LoadingComponent color={bgColors.blue} height="100vh" />;
  }

  return (
    <>
      <Grid container spacing={3} sx={{ mt: 1, mb: 2 }}>
        <Grid
          item
          xs={12}
          md={6}
          sx={{
            display: "flex",
            flexDirection: "column",
            height: { xs: "auto", md: "100%" },
            gap: 3,
          }}
        >
          <Card
            sx={{
              mb: { xs: 2, md: 0 },
              borderRadius: 2,
              boxShadow: "none",
              border: "1.5px solid #EDF2F7",
              background: "#f7f9fb",
              flex: { md: 1, xs: "unset" },
              minHeight: { md: 0 },
              display: "flex",
              flexDirection: "column",
            }}
          >
            <CardContent>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  mb: 3,
                }}
              >
                <Typography
                  variant="h5"
                  sx={{
                      fontWeight: 700,
                      color: "#15192C",
                      fontSize: { xs: 16,  md: 18 },
                    }}
                >
                  Campaign Information
                </Typography>

                <CommonButton
                  primaryAction={{
                    label: " Report",
                    processingLabel: "Downloading...",
                    icon: <FileDownloadOutlinedIcon />,
                    onClick: () => {
                      handleDownloadReport(campaignDetails);
                    },
                  }}
                  onLoading={downloadingReport === campaignDetails?.campaignId}
                  sx={{ pr: 0 }}
                />
              </Box>
              <Box>
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <PeopleAltIcon sx={{ color: "#98A2B3", mr: 2 }} />
                  <Typography
                    sx={{ color: "#667085", fontWeight: 500, flex: 1 }}
                  >
                    Total Contacts
                  </Typography>
                  <Typography
                    sx={{
                      fontWeight: 700,
                      color: "#15192C",
                      minWidth: 80,
                      textAlign: "right",
                    }}
                  >
                    {totalContacts.toLocaleString()}
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <EmailIcon sx={{ color: "#98A2B3", mr: 2 }} />
                  <Typography
                    sx={{ color: "#667085", fontWeight: 500, flex: 1 }}
                  >
                    Template Name
                  </Typography>
                  <Typography
                    sx={{
                      fontWeight: 700,
                      color: "#15192C",
                      minWidth: 80,
                      textAlign: "right",
                    }}
                  >
                    {templateDetails?.templateName}
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <CalendarMonthIcon sx={{ color: "#98A2B3", mr: 2 }} />
                  <Typography
                    sx={{ color: "#667085", fontWeight: 500, flex: 1 }}
                  >
                    Created By
                  </Typography>
                  <Typography
                    sx={{
                      fontWeight: 700,
                      color: "#15192C",
                      minWidth: 80,
                      textAlign: "right",
                    }}
                  >
                    {campaignDetails?.createdby}
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <CalendarMonthIcon sx={{ color: "#98A2B3", mr: 2 }} />
                  <Typography
                    sx={{ color: "#667085", fontWeight: 500, flex: 1 }}
                  >
                    Created Date
                  </Typography>
                  <Typography
                    sx={{
                      fontWeight: 700,
                      color: "#15192C",
                      textAlign: "right",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {formatDateDMY(campaignDetails?.createdDate)}
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <CalendarMonthIcon sx={{ color: "#98A2B3", mr: 2 }} />
                  <Typography
                    sx={{ color: "#667085", fontWeight: 500, flex: 1 }}
                  >
                    Date Set Live
                  </Typography>
                  <Typography
                    sx={{
                      fontWeight: 700,
                      color: "#15192C",
                      textAlign: "right",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {formatDateDMY(campaignDetails?.dateSetLive)}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
          <Card
            sx={{
              borderRadius: 2,
              boxShadow: "none",
              border: "1.5px solid #EDF2F7",
              background: "#f7f9fb",
              flex: { md: 1, xs: "unset" },
              minHeight: { md: 0 },
              display: "flex",
              flexDirection: "column",
            }}
          >
            <CardContent>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  mb: 3,
                }}
              >
                <Typography
                  variant="h5"
                  sx={{
                      fontWeight: 700,
                      color: "#15192C",
                      fontSize: { xs: 16, md: 18 },
                    }}
                >
                  Rerun Campaign Details
                </Typography>
                <CommonButton
                  primaryAction={{
                    label: " Rerun Campaign",
                    icon: null,
                    onClick: handleOpenResendDialog,
                  }}
                  sx={{ pr: 0 }}
                />
              </Box>
              <Box>
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <Typography
                    sx={{ color: "#667085", fontWeight: 500, flex: 1 }}
                  >
                    Failed Contacts
                  </Typography>
                  <Typography
                    sx={{
                      fontWeight: 700,
                      color: "#15192C",
                      minWidth: 80,
                      textAlign: "right",
                    }}
                  >
                    {failedContacts}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Box>
            <DevicePreviewComponent
              header={templateDetails?.header}
              body={templateDetails?.body}
              footer={templateDetails?.footer}
              mediaType={templateDetails?.mediaType}
              mediaFile={templateDetails?.mediaFile}
              buttons={templateDetails?.buttons || []}
              carouselCards={templateDetails?.carouselCards}
            />
          </Box>
        </Grid>
      </Grid>

      <ResendCampaignPopup
        open={openResendDialog}
        handleClose={handleCloseResendDialog}
        totalContacts={totalContacts}
        failedContacts={failedContacts}
        undeliveredContacts={undeliveredContacts}
        campaignDetails={campaignDetails}
        campaignAnalyticsDetails={campaignAnalyticsDetails}
        rerunType={rerunType}
        setRerunType={setRerunType}
        onSuccess={() => {
          handleCloseResendDialog();
          refreshCampaignData && refreshCampaignData();
        }}
        // Add any other props needed for rerun logic
      />
    </>
  );
};

export default CampaignInformation;
