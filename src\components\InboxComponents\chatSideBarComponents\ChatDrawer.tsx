import * as React from "react";
import { styled, useTheme } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Drawer from "@mui/material/Drawer";
import CssBaseline from "@mui/material/CssBaseline";
import Divider from "@mui/material/Divider";
import IconButton from "@mui/material/IconButton";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import ChatSideBar from "../ChatSideBar";
import AccountMetricsBar from "../inboxDetailsComponents/AccountMetricsBar";

const drawerWidth = "60%";

const DrawerHeader = styled("div")(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  padding: theme.spacing(0, 1),
  // necessary for content to be below app bar
  ...theme.mixins.toolbar,
  justifyContent: "flex-end",
}));

export default function PersistentDrawerLeft(props: any) {
  const theme = useTheme();
  //   const [open, setOpen] = React.useState(false);

  //   const handleDrawerOpen = () => {
  //     setOpen(true);
  //   };

  const handleDrawerClose = () => {
    props.handleCloseDrawer(false);
  };

  return (
    <Box sx={{ display: { sm: "flex", md: "none" } }}>
      <CssBaseline />
      <Drawer
        sx={{
          width: drawerWidth,
          "& .MuiDrawer-paper": {
            width: drawerWidth,
            boxSizing: "border-box",
          },
        }}
        variant="persistent"
        anchor="left"
        open={props?.open}
      >
        <AccountMetricsBar handleCloseDrawer={props.handleCloseDrawer} />
        <ChatSideBar
          handleCloseDrawer={props.handleCloseDrawer}
          contacts={props?.contacts}
          setContacts={props?.setContacts}
          searchInput={props?.searchInput}
          setSearchInput={props?.setSearchInput}
          chatsPageNumber={props?.chatsPageNumber}
          setChatsPageNumber={props?.setChatsPageNumber}
          filterData={props?.filterData}
          setFilterData={props?.setFilterData}
          setContactNumber={props?.setContactNumber}
          contactsListLoading={props?.contactsListLoading}
          setContactsListLoading={props?.setContactsListLoading}
          dateRangeFilter={props?.dateRangeFilter}
          setDateRangeFilter={props?.setDateRangeFilter}
        />
      </Drawer>
    </Box>
  );
}
