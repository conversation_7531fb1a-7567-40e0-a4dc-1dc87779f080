import {
  <PERSON>,
  <PERSON>,
  Button,
  Tooltip,
  <PERSON>ir<PERSON><PERSON><PERSON><PERSON>,
  TextField,
  SxProps,
  Theme,
} from "@mui/material";
import { Typography } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import { Search as SearchIcon } from "@mui/icons-material";
interface CommonHeaderProps {
  title?: string;
  count?: number;
  primaryAction?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
    tooltip?: string;
    disabled?: boolean;
    show?: boolean;
    processingLabel?: string;
  };
  searchProps?: {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
  };
  onLoading?: string | boolean;
  onPageChange?: (event: React.ChangeEvent<unknown>, value: number) => void;
  sx?: SxProps<Theme>;
}

const CommonHeader = ({
  title,
  count,
  primaryAction,
  onLoading,
  searchProps,
  onPageChange,
  sx,
}: CommonHeaderProps) => {
  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        gap: 1,
        flex: 1,
        ...sx,
      }}
    >
      <Typography variant="h5" fontWeight="600">
        {title}
      </Typography>
      {count !== undefined && (
        <Chip
          label={count}
          variant="outlined"
          size="small"
          sx={{ backgroundColor: "#f5f5f5", color: "#666", ml: 1 }}
        />
      )}
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        {searchProps && (
          <TextField
            fullWidth
            size="small"
            placeholder={searchProps.placeholder || "Search..."}
            value={searchProps.value}
            onChange={(e) => {
              searchProps.onChange(e.target.value);
              onPageChange && onPageChange({} as any, 1);
            }}
            InputProps={{
              startAdornment: <SearchIcon sx={{ color: "#666", mr: 1 }} />,
            }}
            sx={{
              flexGrow: { xs: 1, md: 0 }, // Take full width on mobile
              width: { xs: "100%", md: 300 },
              "& .MuiOutlinedInput-root": {
                backgroundColor: "#fff",
                "&:hover fieldset": {
                  borderColor: "#3b82f6",
                },
                "&.Mui-focused fieldset": {
                  borderColor: "#3b82f6",
                },
              },
            }}
          />
        )}

        {/* Primary Action Button */}
        {primaryAction && primaryAction.show !== false && (
          <Box sx={{ ml: 2 }}>
            <Tooltip title={primaryAction.tooltip || ""}>
              <Button
                variant="contained"
                size="small"
                startIcon={
                  !onLoading ? primaryAction.icon : null
                }
                onClick={primaryAction.onClick}
                disabled={primaryAction.disabled}
                sx={{
                  textTransform: "none",
                  backgroundColor: "#22c55e",
                  "&:hover": { backgroundColor: "#16a34a" },
                  whiteSpace: "nowrap",
                }}
              >
                {onLoading
                  ? primaryAction.processingLabel
                  : primaryAction.label}
              </Button>
            </Tooltip>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default CommonHeader;
