import React, { useState, ReactNode, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  Typography,
  Box,
  IconButton,
  Pagination,
  Chip,
  Tooltip,
  Fade,
  Popover,
  CircularProgress,
} from "@mui/material";
import { Delete as DeleteIcon, Close as CloseIcon } from "@mui/icons-material";
import FilterListIcon from "@mui/icons-material/FilterList";
import ClearIcon from "@mui/icons-material/Clear";
import RenderToolbar from "./RenderToolBar";
import { useIsMobile } from "../../utils/use-mobile";
import LoadingComponent from "./LoadingComponent";
import { bgColors } from "../../utils/bgColors";
import RestoreIconSvg from "../../assets/svgs/RestoreIconSvg";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import WalletBalanceDisplay from "./WalletBalanceDisplay";
import ColumnFilterPopover, { FilterOption } from "./ColumnFilterPopover";
import DateRangeFilterPopover, { DateRange } from "./DateRangeFilterPopover";
import ScheduledDateRangeFilterPopover from "./ScheduledDateRangeFilterPopover";

export interface TableColumn {
  id: string;
  subId?: string;
  label: string;
  align?: "left" | "right" | "center";
  format?: (value: any) => React.ReactNode;
  width?: string;
  disablePadding?: boolean;
  truncate?: {
    maxItems: number;
    tooltipPlacement?: "top" | "right" | "bottom" | "left";
  };
  filterable?: boolean;
  filterOptions?: FilterOption[];
}

export interface CommonTableProps {
  columns?: TableColumn[];
  data?: Record<string, any>[];
  rowIdKey?: string;
  title?: string;
  count?: number;
  selectable?: boolean;
  onDelete?: (selectedIds: string[]) => void;
  actions?: (row: Record<string, any>) => React.ReactNode;
  page?: number;
  onPageChange?: (event: React.ChangeEvent<unknown>, value: number) => void;
  totalPages?: number;
  showPagination?: boolean;
  selectedMainFilter?: {
    id: string;
    value: string;
  };
  renderOnMobile?: (
    row: Record<string, any>,
    isItemSelected: boolean,
    handleCheckboxClick: (event: any) => void
  ) => ReactNode;
  handleMainFilter?: (event: React.MouseEvent<HTMLElement>) => void;
  searchProps?: {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
  };
  primaryAction?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
    tooltip?: string;
    disabled?: boolean;
    show?: boolean;
  };
  optionalTypographyInRenderToolbar?: string;
  defaultToolbarActions?: Array<{
    icon: React.ReactNode;
    label: string;
    onClick: (event?: any) => void;
    tooltip?: string;
    disabled?: boolean;
    show?: boolean;
  }>;
  selectedItemsToolbarActions?: Array<{
    icon: React.ReactNode;
    label: string;
    onClick: (event?: any) => void;
    tooltip?: string;
    disabled?: boolean;
    show?: boolean;
  }>;
  tagsProps?: {
    onTagsClick: (event: React.MouseEvent<HTMLElement>) => void;
    tagsAnchorEl: HTMLElement | null;
    onTagsClose: () => void;
    tagsList: any[];
    onTagSelect?: (tag: string) => void;
    onTagDelete?: (tag: string) => void;
    hasTagDeletePermission?: boolean;
    show?: boolean;
  };
  isLoading?: boolean;
  selectedItemIds?: string[];
  setSelectedItemIds?: any;
  onRestore?: () => void;
  heightOfTable?: string;
  perPage?: number;
  perPageOptions?: number[];
  onPerPageChange?: (value: number) => void;
  actionsLabel?: string | null;
  onRowClick?: (
    row: Record<string, any>,
    event: React.MouseEvent<HTMLTableRowElement>
  ) => void;
  noDataMessage?: string;
  onColumnFilter?: (columnId: string, selectedValue: string) => void;
  onClearColumnFilter?: (columnId: string) => void;
  columnFilters?: Record<string, string>;
  columnFilterLoading?: Record<string, boolean>;
  useScheduledDateFilter?: boolean;
}

const CommonTable: React.FC<CommonTableProps> = ({
  columns,
  data,
  rowIdKey,
  title,
  count,
  selectable = false,
  onDelete,
  actions,
  page = 1,
  onPageChange,
  totalPages = 1,
  showPagination = true,
  selectedMainFilter,
  renderOnMobile,
  handleMainFilter,
  searchProps,
  primaryAction,
  optionalTypographyInRenderToolbar,
  defaultToolbarActions,
  selectedItemsToolbarActions,
  tagsProps,
  isLoading,
  selectedItemIds = [],
  setSelectedItemIds = () => {},
  onRestore,
  heightOfTable,
  actionsLabel,
  perPage = 10,
  perPageOptions = [10, 25, 50, 100],
  onPerPageChange,
  onRowClick,
  noDataMessage = "No data found",
  onColumnFilter,
  onClearColumnFilter,
  columnFilters = {},
  columnFilterLoading = {},
  useScheduledDateFilter = false,
}) => {
  const isMobile = useIsMobile();

  const [isAllItemsSelected, setIsAllItemsSelected] = useState(false);
  const [columnFilterAnchorEl, setColumnFilterAnchorEl] = useState<HTMLElement | null>(null);
  const [activeColumnFilter, setActiveColumnFilter] = useState<string | null>(null);

  const start = (page - 1) * perPage + 1;
  const end = start + (data?.length || 0) - 1;

  useEffect(() => {
    setIsAllItemsSelected(!!isAllItemsSelectedInCurrentPage());
  }, [data, selectedItemIds.length]);

  const handleSelectAll = (checked: boolean) => {
    if (!setSelectedItemIds) return;
    if (checked) {
      setSelectedItemIds((prev: any) => [
        ...prev.filter(
          (id: any) =>
            !data?.some(
              (item: any) => item[rowIdKey as keyof typeof item] === id
            )
        ),
        ...(data?.map((item: any) => item[rowIdKey as keyof typeof item]) ||
          []),
      ]);
    } else {
      setSelectedItemIds((prev: any) =>
        prev.filter(
          (id: any) =>
            !data
              ?.map((item: any) => item[rowIdKey as keyof typeof item])
              .includes(id)
        )
      );
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (!setSelectedItemIds) return;
    if (checked) {
      setSelectedItemIds((prev: any) => [...prev, id]);
    } else {
      setSelectedItemIds((prev: any) =>
        prev.filter((itemId: any) => itemId !== id)
      );
    }
  };

  const isSelected = (id: string) => selectedItemIds.includes(id);

  const handleRowClick = (
    event: React.MouseEvent<HTMLTableRowElement>,
    id: string
  ) => {
    if ((event.target as HTMLElement).closest(".action-column")) {
      return;
    }

    const isCurrentlySelected = isSelected(id);

    handleSelectItem(id, !isCurrentlySelected);
  };

  const handleRemoveSelected = () => {
    if (onDelete) {
      onDelete(selectedItemIds);
    }
  };

  const handleRestore = () => {
    if (onRestore) {
      onRestore();
    }
  };

  const handleClearSelection = () => {
    setSelectedItemIds([]);
  };

  const handleColumnFilterClick = (event: React.MouseEvent<HTMLElement>, columnId: string) => {
    setColumnFilterAnchorEl(event.currentTarget);
    setActiveColumnFilter(columnId);
  };

  const handleColumnFilterClose = () => {
    setColumnFilterAnchorEl(null);
    setActiveColumnFilter(null);
  };

  const handleApplyColumnFilter = (columnId: string, selectedValue: string) => {
    if (onColumnFilter) {
      onColumnFilter(columnId, selectedValue);
    }
    // Close the popover after applying filter
    handleColumnFilterClose();
  };

  const handleClearColumnFilter = (columnId: string) => {
    if (onClearColumnFilter) {
      onClearColumnFilter(columnId);
    }
  };

  const renderTruncatedArray = (items: any[], column: TableColumn) => {
    if (!Array.isArray(items) || items.length === 0) {
      return null;
    }

    const truncateConfig = column.truncate || { maxItems: 3 };
    const { maxItems, tooltipPlacement = "top" } = truncateConfig;

    // Safely process items to ensure they are strings
    const processedItems = items.map((item) => {
      if (item === null || item === undefined) return "";
      return String(item[column.subId || "tag"]);
    });

    const displayItems = processedItems.slice(0, maxItems);
    const remainingCount =
      processedItems.length > maxItems ? processedItems.length - maxItems : 0;

    return (
      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
        {displayItems.map((item, idx) => (
          <Chip
            key={idx}
            label={item}
            size="small"
            sx={{
              backgroundColor: "#ebf5ff",
              color: "#3b82f6",
              borderColor: "#bfdbfe",
              fontSize: "0.7rem",
            }}
          />
        ))}

        {remainingCount > 0 && (
          <Tooltip
            title={
              <Box sx={{ p: 1, maxWidth: 280 }}>
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                  {processedItems.slice(maxItems).map((item, idx) => (
                    <Chip
                      key={idx}
                      label={item}
                      size="small"
                      sx={{
                        m: 0.5,
                        backgroundColor: "#ebf5ff",
                        color: "#3b82f6",
                        borderColor: "#bfdbfe",
                        fontSize: "0.7rem",
                      }}
                    />
                  ))}
                </Box>
              </Box>
            }
            placement={tooltipPlacement}
            arrow
          >
            <Chip
              label={`+${remainingCount}`}
              size="small"
              variant="outlined"
              sx={{
                backgroundColor: "#f5f5f5",
                color: "#3b82f6",
                borderColor: "#bfdbfe",
                fontSize: "0.7rem",
                cursor: "pointer",
              }}
            />
          </Tooltip>
        )}
      </Box>
    );
  };

  const renderCellContent = (row: Record<string, any>, column: TableColumn) => {
    const value = row[column.id];

    if (column.truncate && Array.isArray(value)) {
      return renderTruncatedArray(value, column);
    }

    if (column.format) {
      return column.format(value);
    }

    return value;
  };

  const getSelectedItems = () => {
    return data?.filter((item) =>
      selectedItemIds.includes(item[rowIdKey as keyof typeof item])
    );
  };

  function isAllItemsSelectedInCurrentPage() {
    return (
      selectedItemIds.length > 0 &&
      data?.every((item) =>
        selectedItemIds.some((id) => item[rowIdKey as keyof typeof item] === id)
      )
    );
  }

  // Helper to calculate correct colSpan for loading/empty rows
  const getColSpan = () => {
    let colSpan = columns?.length || 0;
    if (selectable) colSpan += 1;
    if (actions) colSpan += 1;
    return colSpan;
  };

  return (
    <Paper
      elevation={1}
      sx={{
        width: "100%",
        height: heightOfTable || "100vh",
        display: "flex",
        flexDirection: "column",
        overflow: "visible", // Changed from "hidden" to "visible" to allow child scrolling
      }}
    >
      {title && (
        <Box
          sx={{
            px: 3,
            py: 1,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            justifyContent: "space-between",
            alignItems: "center",
            borderBottom: "1px solid #f0f0f0",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography variant="h5" fontWeight="600">
              {title}
            </Typography>
            {count !== undefined && (
              <Chip
                label={count}
                variant="outlined"
                size="small"
                sx={{ backgroundColor: "#f5f5f5", color: "#666", ml: 1 }}
              />
            )}
          </Box>
          {(title === "Campaigns" || title === "Scheduled Campaigns") && (
            <WalletBalanceDisplay />
          )}
        </Box>
      )}
      {(selectedMainFilter || primaryAction) && (
        <RenderToolbar
          selectedMainFilter={selectedMainFilter}
          handleMainFilter={handleMainFilter}
          searchProps={searchProps}
          onPageChange={onPageChange}
          primaryAction={
            selectedItemIds?.length === 0 ? primaryAction : undefined
          }
          optionalTypographyInRenderToolbar={optionalTypographyInRenderToolbar}
          toolbarActions={
            selectedItemIds.length === 0
              ? defaultToolbarActions
              : selectedItemsToolbarActions
          }
          tagsProps={tagsProps}
        />
      )}

      {isMobile && isLoading && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: heightOfTable || "calc(100vh - 220px)",
          }}
        >
          <LoadingComponent height="500px" color={bgColors.blue} />
        </Box>
      )}

      {isMobile && Boolean(renderOnMobile) && (
        <Box
          sx={{ overflow: "auto", height: "calc(100vh - 220px)", flex: "1" }}
        >
          {data?.map((row: any) => {
            const isItemSelected = selectable
              ? isSelected(row[rowIdKey as keyof typeof row])
              : false;
            const handleCheckboxClick = (event: any) => {
              selectable &&
                handleRowClick(event, row[rowIdKey as keyof typeof row]);
            };

            return renderOnMobile?.(row, isItemSelected, handleCheckboxClick);
          })}
        </Box>
      )}

      {!isMobile && (
        <TableContainer
          sx={{
            height: `calc(${heightOfTable} || "100vh" - 220px)`,
            flex: 1,
            overflow: "auto", // Enable both horizontal and vertical scrolling
            maxWidth: "100%", // Ensure container doesn't exceed parent width
            "&::-webkit-scrollbar": {
              height: "8px",
              width: "8px",
            },
            "&::-webkit-scrollbar-track": {
              backgroundColor: "#f1f1f1",
              borderRadius: "4px",
            },
            "&::-webkit-scrollbar-thumb": {
              backgroundColor: "#c1c1c1",
              borderRadius: "4px",
              "&:hover": {
                backgroundColor: "#a8a8a8",
              },
            },
            "&::-webkit-scrollbar-corner": {
              backgroundColor: "#f1f1f1",
            },
            // Firefox scrollbar styling
            scrollbarWidth: "thin",
            scrollbarColor: "#c1c1c1 #f1f1f1",
          }}
        >
          <Table
            stickyHeader
            size="small"
            sx={{
              borderCollapse: "separate",
              tableLayout: "auto", // Changed from "fixed" to "auto" for better column width handling
              minWidth: "max-content", // Ensure table expands to fit content, triggering horizontal scroll when needed
              width: "100%", // Take full width when content fits
            }}
          >
            <TableHead>
              <TableRow>
                {selectable && (
                  <TableCell
                    sx={{
                      backgroundColor: "#F2F2F2",
                      borderBottom: "1px solid #e0e0e0",
                    }}
                    width="60px"
                  >
                    <Checkbox
                      checked={isAllItemsSelected}
                      indeterminate={
                        data?.some((item: any) =>
                          selectedItemIds.includes(
                            item[rowIdKey as keyof typeof item]
                          )
                        ) && !isAllItemsSelected
                      }
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      size="small"
                    />
                  </TableCell>
                )}

                {columns?.map((column) => (
                  <TableCell
                    key={column.id}
                    align="left"
                    sx={{
                      backgroundColor: "#F2F2F2",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#666",
                      fontSize: "0.875rem",
                      fontWeight: 500,
                      height: "48px",
                      whiteSpace: "nowrap",
                    }}
                  >
                    <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                      <Typography>{column.label}</Typography>
                      {column.filterable && column.filterOptions && (
                        <Box sx={{ position: "relative" }}>
                          <IconButton
                            size="small"
                            onClick={(e) => handleColumnFilterClick(e, column.id)}
                            disabled={columnFilterLoading[column.id]}
                            sx={{
                              padding: "2px",
                              color: columnFilters[column.id] ? bgColors.green : "#666",
                              "&:hover": {
                                backgroundColor: "rgba(68, 71, 70, 0.08)",
                                color: bgColors.green,
                              },
                              "&:disabled": {
                                color: "#ccc",
                              },
                            }}
                          >
                            {columnFilterLoading[column.id] ? (
                              <CircularProgress size={16} color="inherit" />
                            ) : (
                              <FilterListIcon fontSize="small" />
                            )}
                          </IconButton>
                          {columnFilters[column.id] && (
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleClearColumnFilter(column.id);
                              }}
                              sx={{
                                position: "absolute",
                                top: "-6px",
                                right: "-6px",
                                width: "16px",
                                height: "16px",
                                minWidth: "16px",
                                backgroundColor: bgColors.red,
                                color: "white",
                                padding: 0,
                                fontSize: "10px",
                                "&:hover": {
                                  backgroundColor: "#dc2626",
                                  transform: "scale(1.1)",
                                },
                                zIndex: 1,
                              }}
                            >
                              <ClearIcon sx={{ fontSize: "10px" }} />
                            </IconButton>
                          )}
                        </Box>
                      )}
                    </Box>
                  </TableCell>
                ))}

                {actions && actionsLabel !== null && (
                  <TableCell
                    align="right"
                    sx={{
                      backgroundColor: "#F2F2F2",
                      borderBottom: "1px solid #e0e0e0",
                      color: "#666",
                      fontSize: "0.875rem",
                      fontWeight: 500,
                      textAlign: "left",
                      width: "150px",
                    }}
                  >
                    {actionsLabel || "Action"}
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading && (
                <TableRow>
                  <TableCell colSpan={getColSpan()}>
                    <LoadingComponent height="500px" color={bgColors.blue} />
                  </TableCell>
                </TableRow>
              )}
              {!isLoading && data && data?.length === 0 && (
                <TableRow>
                  <TableCell
                    colSpan={getColSpan()} // use helper for correct colSpan
                    align="center"
                    sx={{ border: "none" }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: 150, // you can adjust the height
                        width: "100%",
                      }}
                    >
                      <Typography variant="body2" color="text.secondary">
                        {noDataMessage}
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              )}
              {!isLoading &&
                data?.map((row) => {
                  const isItemSelected = selectable
                    ? isSelected(row[rowIdKey as keyof typeof row])
                    : false;

                  return (
                    <TableRow
                      key={row[rowIdKey as keyof typeof row]}
                      selected={isItemSelected}
                      hover
                      onClick={(event) => {
                        if (onRowClick) {
                          onRowClick(row, event);
                        } else if (selectable) {
                          handleRowClick(
                            event,
                            row[rowIdKey as keyof typeof row]
                          );
                        }
                      }}
                      sx={{
                        "&:hover": {
                          backgroundColor: "#f5f5f5",
                        },
                        "&.Mui-selected": {
                          backgroundColor: "#ebf3ff !important",
                        },
                        cursor:
                          onRowClick || selectable ? "pointer" : "default",
                        height: "52px",
                      }}
                    >
                      {selectable && (
                        <TableCell
                          sx={{
                            borderBottom: "1px solid #f0f0f0",
                          }}
                          width="60px"
                        >
                          <Checkbox
                            checked={isItemSelected}
                            size="small"
                            onClick={(e) => e.stopPropagation()}
                            onChange={(e) =>
                              handleSelectItem(
                                row[rowIdKey as keyof typeof row],
                                e.target.checked
                              )
                            }
                          />
                        </TableCell>
                      )}

                      {columns &&
                        columns?.map((column) => (
                          <TableCell
                            key={column.id}
                            align="left"
                            sx={{
                              borderBottom: "1px solid #f0f0f0",
                              color: column.id === "name" ? "#000" : "#666",
                              fontSize: "0.875rem",
                              height: "52px",
                              whiteSpace: "nowrap",
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                            }}
                          >
                            {renderCellContent(row, column)}
                          </TableCell>
                        ))}

                      {actions && (
                        <TableCell
                          align="left"
                          className="action-column"
                          onClick={(e) => e.stopPropagation()}
                          sx={{
                            borderBottom: "1px solid #f0f0f0",
                            width: "150px",
                          }}
                        >
                          {actions(row)}
                        </TableCell>
                      )}
                    </TableRow>
                  );
                })}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Column Filter Popover */}
      {activeColumnFilter && (
        (activeColumnFilter === "createdDate" || activeColumnFilter === "dateSetLive") ? (
          useScheduledDateFilter ? (
            <ScheduledDateRangeFilterPopover
              anchorEl={columnFilterAnchorEl}
              handleClose={handleColumnFilterClose}
              onApplyFilter={(dateRange: DateRange) => {
                handleApplyColumnFilter(activeColumnFilter, JSON.stringify(dateRange));
              }}
              initialDateRange={columnFilters[activeColumnFilter] ? JSON.parse(columnFilters[activeColumnFilter]) : undefined}
              title={`Filter by ${columns?.find(col => col.id === activeColumnFilter)?.label || "Date"}`}
            />
          ) : (
            <DateRangeFilterPopover
              anchorEl={columnFilterAnchorEl}
              handleClose={handleColumnFilterClose}
              onApplyFilter={(dateRange: DateRange) => {
                handleApplyColumnFilter(activeColumnFilter, JSON.stringify(dateRange));
              }}
              initialDateRange={columnFilters[activeColumnFilter] ? JSON.parse(columnFilters[activeColumnFilter]) : undefined}
              title={`Filter by ${columns?.find(col => col.id === activeColumnFilter)?.label || "Date"}`}
            />
          )
        ) : (
          <ColumnFilterPopover
            anchorEl={columnFilterAnchorEl}
            handleClose={handleColumnFilterClose}
            columnId={activeColumnFilter}
            columnLabel={columns?.find(col => col.id === activeColumnFilter)?.label || ""}
            filterOptions={columns?.find(col => col.id === activeColumnFilter)?.filterOptions || []}
            selectedFilter={columnFilters[activeColumnFilter] || null}
            onApplyFilter={handleApplyColumnFilter}
            onClearFilter={handleClearColumnFilter}
          />
        )
      )}

      {showPagination && (
        <Box
          sx={{
            p: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            borderTop: "1px solid #f0f0f0",
            backgroundColor: "#fff",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            {isMobile ? (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ fontSize: "0.7rem", ml: 0 }}
              >
                {start} - {end} / {count || data?.length}
              </Typography>
            ) : (
              <Typography variant="body2" color="text.secondary">
                {`Showing ${start}-`}
                {isLoading ? (
                  <CircularProgress
                    size={16}
                    color="success"
                    sx={{ verticalAlign: "middle" }}
                  />
                ) : (
                  end
                )}
                {` of `}
                {isLoading ? (
                  <CircularProgress
                    size={16}
                    color="success"
                    sx={{ verticalAlign: "middle" }}
                  />
                ) : (
                  count || data?.length
                )}
                {` items`}
              </Typography>
            )}
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                {isMobile ? "RPP:" : "Rows per page: "}
              </Typography>
              <Select
                size="small"
                value={perPage}
                onChange={(e) => onPerPageChange?.(Number(e.target.value))}
                sx={{
                  minWidth: 72,
                  maxWidth: 80,
                  height: 28,
                  fontSize: "0.8rem",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d1d5db",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d1d5db",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d1d5db",
                  },
                }}
              >
                {(perPageOptions || [10, 25, 50, 100, 200, 500]).map(
                  (option) => (
                    <MenuItem key={option} value={option}>
                      {option}
                    </MenuItem>
                  )
                )}
              </Select>
            </Box>
          </Box>

          <Pagination
            count={totalPages}
            page={page}
            onChange={onPageChange}
            variant="outlined"
            shape="rounded"
            size="small"
            sx={{
              "& .MuiPaginationItem-root": {
                margin: isMobile ? "0 2px" : "0 4px",
                minWidth: isMobile ? 20 : 32,
                height: isMobile ? 20 : 32,
                fontSize: isMobile ? "0.6rem" : "0.875rem",
                padding: isMobile ? "1px 2px" : "6px 8px",
              },
              fontSize: isMobile ? "0.6rem" : "0.875rem",
            }}
          />
        </Box>
      )}

      <Fade in={selectable && selectedItemIds.length > 0}>
        <Box
          sx={{
            position: "fixed",
            bottom: "80px",
            left: isMobile ? "auto" : "50%",
            transform: isMobile ? "none" : "translateX(-50%)",
            zIndex: 1000,
            minWidth: "340px",
            boxShadow: "0px 4px 12px rgba(0,0,0,0.15)",
            borderRadius: "8px",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: isMobile ? 1 : 2,
              backgroundColor: "#fff",
              p: 2,
              borderRadius: "8px",
              border: "1px solid #bfdbfe",
            }}
          >
            <Box
              sx={{
                ml: 2,
                cursor: "pointer",
                "&:hover": { textDecoration: "underline" },
              }}
            >
              <Typography variant="body2" fontWeight="500">
                {selectedItemIds.length} selected items
              </Typography>
            </Box>

            {onDelete && (
              <Box
                onClick={handleRemoveSelected}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                  gap: 0.5,
                  p: 0.5,
                  borderRadius: 1,
                  "&:hover": { backgroundColor: "rgba(0,0,0,0.05)" },
                }}
              >
                <DeleteIcon fontSize="small" color="error" />
                <Typography variant="body2" color="error">
                  Remove items
                </Typography>
              </Box>
            )}
            {onRestore && (
              <Box
                onClick={handleRestore}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                  gap: 0.5,
                  p: 0.5,
                  borderRadius: 1,
                  "&:hover": { backgroundColor: "rgba(0,0,0,0.05)" },
                }}
              >
                <RestoreIconSvg />
                <Typography variant="body2" color="primary">
                  Restore items
                </Typography>
              </Box>
            )}

            <IconButton
              size="small"
              onClick={handleClearSelection}
              sx={{ ml: "auto", mr: 1 }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        </Box>
      </Fade>
    </Paper>
  );
};

export default CommonTable;
