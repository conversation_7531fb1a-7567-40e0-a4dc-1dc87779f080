import React from "react";
import {
  Grid,
  Box,
  Typography,
  InputLabel,
  FormControl,
  Select,
  MenuItem,
  FormHelperText,
  RadioGroup,
  FormControlLabel,
  Radio,
  IconButton,
  Tooltip,
  CircularProgress,
  Button,
} from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import { makeStyles } from "@mui/styles";
import { bgColors } from "../../../utils/bgColors";
import TextFieldWithBorderComponent from "../../common/TextFieldWithBorderComponent";
import FileUpload from "../../common/FileUpload";
import EmojiPopover from "../../InboxComponents/inboxDetailsComponents/EmojiPicker";
import ActionButtonsComponent from "./actionButtons";
import DevicePreviewComponent from "./devicePreview";
import { useEffect, useRef, useState } from "react";
import { EditorState, ContentBlock, Modifier, convertToRaw } from "draft-js";
import "draft-js/dist/Draft.css";
import { DraftEditorComponent } from "../../common/DraftEditorComponent";
import { reactDraftWysiwygToolbarOptionsarticle } from "../../../utils/react-draft-wysiwyg-options";
import LanguagesList from "../../../utils/languagesList.json";
import { formatContent, parseTextToDraft } from "../../../utils/functions";
import { countTemplateVariables } from "./functions";
import { predefinedVariables, mediaTypes } from "../../../utils/constants";
import AuthenticationCategory from "./AuthenticationCategory";
import Carousel from "./Carousel";
import {
  TemplateDialogContentProps,
  ButtonType,
} from "../../../utils/interfaces";

const useStyles = makeStyles({
  blackColor: {
    color: "#303030 !important",
    fontWeight: "500 !important",
  },
  variable: {
    color: `${bgColors.green} !important`,
    fontWeight: "500 !important",
    fontSize: "14px !important",
    cursor: "pointer",
  },
  formControl: {
    display: "flex",
    alignItems: "center",
  },
  disable: {
    pointerEvents: "none",
    opacity: "0.5",
    cursor: "not-allowed",
  },
});

const muiSelectNoBlue = `
.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #e0e0e0 !important;
}
.MuiInputLabel-root.Mui-focused {
  color: #757575 !important;
}
`;
const selectStyles = {
  "& .MuiOutlinedInput-root": {
    height: "40px",
    "& fieldset , & .MuiOutlinedInput-notchedOutline": {
      borderColor: "#e0e0e0 !important",
    },
    "&:hover fieldset , &:hover .MuiOutlinedInput-notchedOutline": {
      borderColor: "#bdbdbd !important",
    },
    "&.Mui-focused fieldset , &.Mui-focused .MuiOutlinedInput-notchedOutline": {
      borderColor: "#e0e0e0 !important",
    },
  },
  "& .MuiInputLabel-root": {
    fontSize: "14px !important",
    color: "#757575 !important",
    "&.Mui-focused": {
      color: "#757575 !important",
    },
  },
  "& .MuiSelect-select": {
    fontSize: "14px !important",
    color: "#757575 !important",
    "&:focus": {
      color: "#757575 !important",
    },
  },
};

const menuItemStyles = {
  fontSize: "14px !important",
  "&:hover": {
    backgroundColor: "rgba(76, 175, 80, 0.08) !important",
  },
  "&.Mui-selected": {
    backgroundColor: "rgba(76, 175, 80, 0.12) !important",
    "&:hover": {
      backgroundColor: "rgba(76, 175, 80, 0.16) !important",
    },
  },
};

const TemplateDialogContent: React.FC<TemplateDialogContentProps> = ({
  canEdit,
  templateData,
  templateNameSlice,
  setTemplateState,
  templateState,
  formErrors,
  setFormErrors,
  handleChange,
  handleMediaSelectionChange,
  handleHeaderMediaChange,
  handleEmojiSelect,
  handleCloseEmojiPopover,
  handleAddVariable,
  emojiPopoverOpen,
  anchorEl,
  bodyRef,
  headerRef,
  urlButtonRef,
  maxLength,
  phoneNumberButtonCount,
  urlButtonCount,
  replyButtonCount,
  addButton,
  removeButton,
  updateButton,
  buttonsArray,
}) => {
  const classes = useStyles();

  // Get the count of variables
  const { headerVariablesCount, bodyVariablesCount, urlButtonVariablesCount } =
    countTemplateVariables(templateState);

  const chatAreaRef = useRef<HTMLDivElement>(null);

  const [editorState, setEditorState] = useState(() =>
    EditorState.createWithContent(parseTextToDraft(""))
  );

  const [file] = useState<any>(null);

  const [selectedVariable, setSelectedVariable] = useState<string>("");
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleEditorDelayedStateChange = (editorState: EditorState) => {
    const formattedContent = formatContent(editorState.getCurrentContent());
    const event = {
      target: {
        name: "body",
        value: formattedContent,
      },
    };
    handleChange(
      event as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    );

    setEditorState(editorState);
  };

  const blockRendererFn = (contentBlock: ContentBlock) => {
    const type = contentBlock.getType();
    if (type === "atomic") {
      return {
        component: Media,
        editable: false,
      };
    }
    return null;
  };

  const Media = (props: any) => {
    const entity = props.contentState.getEntity(props.block.getEntityAt(0));
    const { src, type } = entity.getData();
    let media;
    if (type === "IMAGE") {
      media = (
        <img
          src={src}
          alt="uploaded"
          style={{ height: "100px", width: "100px" }}
        />
      );
    } else if (type === "VIDEO") {
      media = (
        <video src={src} controls style={{ height: "100px", width: "300px" }} />
      );
    }
    return media;
  };

  const handleSaveInboxSettings = async () => {
    // const formatTime = (hours: number, minutes: number): string => {
    //   const pad = (num: number) => String(num).padStart(2, "0");
    //   return `${pad(hours)}:${pad(minutes)}:00`;
    // };
    // const delay = feature === 2 ? formatTime(selectedDelayHours, selectedDelayMinutes) : null;
    // try {
    //   const data = {
    //     businessId: getLoginData?.companyId,
    //     userId: getLoginData?.userId,
    //     inboxSettings: {
    //       feature: feature,
    //       message: feature === 0 ?  formatContent(editorState.getCurrentContent()) :
    //               feature === 1 ? formatContent(welcomEditorState.getCurrentContent()):
    //               formatContent(delayedEditorState.getCurrentContent()),
    //       enabled: outOfOfficeEnable,
    //       delay: delay,
    //       mediaUrl: fileUrl ? fileUrl : null,
    //     }
    //   }
    //   const getRes = await dispatch(updateInboxSettings(data))
    //   if (getRes?.meta.requestStatus === "fulfilled") {
    //     dispatch(toastActions.setToaster({
    //       type: "success",
    //       message: (getRes?.payload?.message || "Inbox settings updated successfully"),
    //     }))
    //     dispatch(getInboxSettings({ businessId: getLoginData?.companyId, userId: getLoginData?.userId, feature: feature }))
    //     setEditInbox(false)
    //     setEditWelcomeBox(false)
    //   } else {
    //     dispatch(toastActions.setToaster({
    //       type: "error",
    //       message: "Something went wrong",
    //     }))
    //   }
    // } catch (error) {
    // }
  };

  const getNextVariableCount = () => {
    const body = templateState.body;

    // Find all variables in the body text
    const variableMatches = body.match(/\{\{(\d+)\}\}/g);

    if (!variableMatches) return 1; // If no variables found, start with 1

    // Extract the numbers from the variables and convert to integers
    const variableNumbers = variableMatches.map((match) =>
      parseInt(match.replace(/\{\{|\}\}/g, ""), 10)
    );

    // Find the maximum number
    const maxCount = Math.max(...variableNumbers);

    // Return the next number after the maximum
    return maxCount + 1;
  };
  const handleEditorAddHeaderPrebuildVariable = (event: any) => {
    // Get current template state
    const headerText = templateState?.header || "";

    const variable = event?.target?.value;
    if (!variable) return;

    // Determine input references and positions based on type
    let input, startPos, endPos, newText;
    input = headerRef?.current;
    newText = headerText;

    if (!input) return;

    startPos = input.selectionStart ?? 0;
    endPos = input.selectionEnd ?? 0;

    const startPosIndex = startPos || 0;
    const endPosIndex = endPos || 0;

    const updatedText =
      newText?.substring(0, startPosIndex) +
      variable +
      newText?.substring(endPosIndex, newText?.length);

    // Assign default values to startPos and endPos if they are null or undefined

    // Update variables with the proper count for the type
    const newVariable = {
      type: "header",
      id: variable,
      value: "",
      field: "",
      fallBackValue: "",
    };

    // Update the appropriate text based on type

    // Construct the new text with the variable inserted at the correct position

    // Set the new template state
    setTemplateState((prevState: any) => ({
      ...prevState,

      header: updatedText,
      leadratVariables: [...prevState.leadratVariables, newVariable],
    }));

    setSelectedVariable("leadratVariable");
  };

  const handleEditorAddPrebuildVariable = (event: any) => {
    const contentState = editorState.getCurrentContent();
    const selection = editorState.getSelection();
    const variable = event?.target?.value;

    const newContentState = Modifier.replaceText(
      contentState,
      selection,
      variable
    );
    const newEditorState = EditorState.push(
      editorState,
      newContentState,
      "insert-characters"
    );
    setEditorState(newEditorState);
    const formattedContent = formatContent(newContentState);
    const newVariable = {
      type: "body",
      id: variable,
      value: "",
      field: "",
      fallBackValue: "",
    };
    setTemplateState((prevState: any) => ({
      ...prevState,
      body: formattedContent,

      leadratVariables: [...prevState.leadratVariables, newVariable],
    }));
    setSelectedVariable("leadratVariable");
  };

  const handleEditorAddVariable = () => {
    const contentState = editorState.getCurrentContent();
    const selection = editorState.getSelection();

    // Get the next variable count
    const newCount = getNextVariableCount();

    const newVariable = {
      type: "body",
      id: `{{${newCount}}}`,
      value: "",
      field: "",
      fallBackValue: "",
    };

    // Construct the new variables array
    // const newVariables = [...templateState.variables, newVariable];
    const newVariables = [...(templateState?.variables || []), newVariable];

    const variableText = ` {{${newCount}}} `;

    const newContentState = Modifier.replaceText(
      contentState,
      selection,
      variableText
    );

    const newEditorState = EditorState.push(
      editorState,
      newContentState,
      "insert-characters"
    );

    setEditorState(newEditorState);

    // Update the template state
    const formattedContent = formatContent(newContentState);
    setTemplateState((prevState: any) => ({
      ...prevState,
      body: formattedContent,
      variables: newVariables,
    }));
  };

  const handleAddMoreCarousel = () => {
    const uuid = crypto.randomUUID();
    setTemplateState((prevState: any) => ({
      ...prevState,
      carouselCards: [
        ...prevState.carouselCards,
        {
          id: uuid,
          mediaUrlType: 0,
          headerMediaUrl: "",
          body: "",
          carouselButtons: [],
        },
      ],
    }));
    setFormErrors((prev: any) => ({
      ...prev,
      carouselCards: [
        ...prev.carouselCards,
        {
          id: uuid,
          mediaUrlType: "",
          headerMediaUrl: "",
          body: "",
          minimumButton: "",
          carouselButtons: [],
        },
      ],
    }));
  };
  const handleRemoveCarousel = (carouselId: string) => {
    if (
      templateState?.carouselCards &&
      templateState?.carouselCards?.length > 1
    ) {
      setTemplateState((prevState: any) => {
        const carouselIndex = prevState.carouselCards.findIndex(
          (card: any) => card.id === carouselId
        );
        const updatedCarouselCards = prevState.carouselCards.filter(
          (carousel: any) => carousel.id !== carouselId
        );

        // Update the current index in devicePreview
        // If we're deleting the currently selected carousel or one before it,
        // we need to adjust the currentIndex
        if (carouselIndex <= currentIndex) {
          // If we're deleting the last carousel and it's currently selected,
          // move to the new last carousel
          if (
            carouselIndex === prevState.carouselCards.length - 1 &&
            carouselIndex === currentIndex
          ) {
            setCurrentIndex(Math.max(0, updatedCarouselCards.length - 1));
          } else {
            // Otherwise, just decrement the currentIndex to maintain the same relative position
            setCurrentIndex(Math.max(0, currentIndex - 1));
          }
        }

        return {
          ...prevState,
          carouselCards: updatedCarouselCards,
        };
      });

      setFormErrors((prev: any) => {
        const updatedCarouselCards = prev.carouselCards.filter(
          (carousel: any) => carousel.id !== carouselId
        );
        return {
          ...prev,
          carouselCards: updatedCarouselCards,
        };
      });
    }
  };

  const handleCarouselFileChange: any = (
    response: any,
    carouselIndex: any,
    file: any
  ) => {
    const allowedImageTypes = ["image/jpeg", "image/png", "image/gif"];
    const allowedVideoTypes = ["video/mp4", "video/avi", "video/mpeg"];

    let isValidFile = 0;
    isValidFile = allowedImageTypes?.includes(file.type) ? 3 : 0;
    if (!isValidFile) {
      isValidFile = allowedVideoTypes?.includes(file.type) ? 4 : 0;
    }
    let existingMediaType: any = 0;

    if (isValidFile) {
      setTemplateState((prevState: any) => {
        const updatedCarouselCard = {
          ...prevState.carouselCards[carouselIndex],
        };
        updatedCarouselCard.headerMediaUrl = response.payload;
        updatedCarouselCard.mediaUrlType = isValidFile;
        const updatedCarouselCards = prevState.carouselCards.map(
          (carousel: any, i: any) => {
            if (carouselIndex === i) {
              return updatedCarouselCard;
            }
            return carousel;
          }
        );
        updatedCarouselCards.forEach((carousel: any) => {
          if (!existingMediaType) {
            existingMediaType = carousel.mediaUrlType;
          } else {
            if (existingMediaType !== carousel.mediaUrlType) {
              existingMediaType = 0;
              return;
            }
          }
        });
        return {
          ...prevState,
          carouselCards: updatedCarouselCards,
          mediaFile: response.payload,
        };
      });

      setFormErrors((prev: any) => {
        const updatedCarouselCard = { ...prev.carouselCards[carouselIndex] };
        updatedCarouselCard.headerMediaUrl = "";
        const updatedCarouselCards: any = [...prev.carouselCards];
        updatedCarouselCards[carouselIndex] = updatedCarouselCard;
        return {
          ...prev,
          carouselCardsValid:
            existingMediaType === 0 && templateState.mediaFile
              ? "All carousels should have same media type"
              : "",
          carouselCards: updatedCarouselCards,
        };
      });
    } else {
      alert(
        `Only ${
          isValidFile === 3 ? allowedImageTypes : allowedVideoTypes
        } files are allowed.`
      );
      file = null; // Clear the input field if the file is invalid
    }
  };

  useEffect(() => {
    setEditorState(
      EditorState?.createWithContent(
        parseTextToDraft(templateState?.body || "")
      )
    );
  }, [templateState?.templateName]);

  useEffect(() => {
    if (
      templateState.variables?.length === 0 &&
      templateState.leadratVariables?.length === 0
    ) {
      setSelectedVariable("");
    } else if (
      templateState.variables?.length > 0 &&
      templateState.leadratVariables?.length === 0
    ) {
      setSelectedVariable("addVariable");
    } else {
      setSelectedVariable("leadratVariable");
    }
  }, [templateState.variables, templateState.leadratVariables]);

  return (
    <Grid
      container
      ref={chatAreaRef}
      sx={{
        height: "100%",
        display: "flex",
        alignItems: { xs: "center", lg: "start" },
        justifyContent: "space-between",
        gap: { xs: 2, lg: 0 },
      }}
    >
      <Grid
        item
        xs={12}
        lg={7.5}
        sx={{
          overflowY: "auto",
          scrollbarWidth: "none",
          msOverflowStyle: "none",
          "&::-webkit-scrollbar": {
            display: "none",
          },
          flexGrow: 1,
          height: { lg: "100%" },
          padding: " 0 5px 5px 0",
        }}
      >
        <Box mb={{ xs: 1, md: 2 }}>
          <TextFieldWithBorderComponent
            size="small"
            fullWidth
            name="templateName"
            disabled={canEdit && templateData?.status !== 4}
            placeholder=""
            label="Enter template name"
            value={templateState?.templateName?.toLocaleLowerCase() || ""}
            onChange={handleChange}
            sx={{
              "& input": {
                fontSize: "14px", // Set input font size
              },
              "& .MuiOutlinedInput-root": {
                borderRadius: "8px", // Set border radius
              },
            }}
            error={!!formErrors?.templateName}
            helperText={
              templateNameSlice?.status === "loading" ||
              templateNameSlice?.status === "idle" ||
              (canEdit === true && templateData?.status !== 4) ||
              (canEdit === true &&
                templateData?.status === 4 &&
                templateState?.templateName === templateData?.templateName)
                ? ""
                : formErrors?.templateName ||
                  (templateState?.templateName !== "" && (
                    <span style={{ color: bgColors?.green }}>
                      {templateNameSlice?.data?.message}
                    </span>
                  ))
            }
            InputProps={{
              sx: { fontSize: 14 },
              endAdornment: (
                <>
                  {templateNameSlice?.status === "loading" && (
                    <CircularProgress size={20} />
                  )}
                </>
              ),
            }}
          />
        </Box>
        <Box display="flex" width="100%" mb={{ xs: 1, md: 2 }}>
          <Box width="50%" mr={0}>
            <FormControl fullWidth size="small" error={!!formErrors?.category}>
              <InputLabel
                id="demo-simple-select-label"
                sx={{ fontSize: { xs: 12, md: 14 } }}
              >
                Category
              </InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                disabled={canEdit && templateData?.status !== 4}
                value={templateState?.category}
                name="category"
                label="Category"
                inputProps={{ style: { fontSize: 14 } }}
                onChange={handleChange}
                sx={{
                  fontSize: 14,
                  borderRadius: "8px",
                }}
              >
                <MenuItem
                  value={1}
                  key={1}
                  sx={{ fontSize: { xs: 10, md: 12 } }}
                >
                  Marketing
                </MenuItem>
                <MenuItem
                  value={2}
                  key={2}
                  sx={{ fontSize: { xs: 10, md: 12 } }}
                >
                  Utility
                </MenuItem>
                <MenuItem
                  value={3}
                  key={3}
                  sx={{ fontSize: { xs: 10, md: 12 } }}
                >
                  Authentication
                </MenuItem>
              </Select>
              {formErrors?.category && (
                <FormHelperText>{formErrors?.category}</FormHelperText>
              )}
            </FormControl>
          </Box>
          <Box width="50%" ml={1}>
            <FormControl fullWidth size="small" error={!!formErrors?.language}>
              <InputLabel
                id="demo-simple-select-label"
                sx={{ fontSize: { xs: 12, md: 14 } }}
              >
                Language
              </InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                disabled={canEdit && templateData?.status !== 4}
                value={+templateState?.language}
                name="language"
                label="Language"
                inputProps={{ style: { fontSize: 14 } }}
                onChange={handleChange}
                sx={{
                  fontSize: 14,
                  borderRadius: "8px",
                  // fontWeight: "600",
                }}
              >
                {LanguagesList &&
                  LanguagesList?.map((item: any, index: number) => (
                    <MenuItem
                      value={item?.value}
                      key={index}
                      sx={{ fontSize: 14 }}
                    >
                      <Typography
                        sx={{
                          fontSize: 14,
                          fontWeight:
                            item?.value === templateState?.language
                              ? "normal"
                              : "normal",
                        }}
                      >
                        {item?.name}
                      </Typography>
                    </MenuItem>
                  ))}
              </Select>
              {formErrors?.language && (
                <FormHelperText>{formErrors?.language}</FormHelperText>
              )}
            </FormControl>
          </Box>
        </Box>
        {templateState.category === 3 ? (
          <AuthenticationCategory
            handleChange={handleChange}
            templateState={templateState}
            setTemplateState={setTemplateState}
          />
        ) : (
          <Box width="100%">
            <Box width="100%" mb={{ xs: 1, md: 2 }}>
              <TextFieldWithBorderComponent
                variant="outlined"
                // required
                size="small"
                fullWidth
                value={templateState?.subCategory}
                onChange={handleChange}
                name="subCategory"
                placeholder=""
                label="Enter sub-category"
              />
            </Box>
            <Box
              mb={
                templateState?.mediaFile ? { xs: 3, md: 4 } : { xs: 1, md: 2 }
              }
            >
              <InputLabel
                className={classes.blackColor}
                sx={{ fontSize: { xs: 12, md: 14 } }}
              >
                Header*
                {templateState?.mediaType === 2 && (
                  <Tooltip
                    title={
                      <>
                        <div>
                          1. Minimum one letter or word must be present in
                          between any two variables.
                        </div>
                        <div>
                          2. Header cannot be started or ended with a variable.
                        </div>
                        <div>3. Only one variable is allowed in header.</div>
                        <div> 4.Header text must not exceed 60 characters.</div>
                        <div>
                          5.Header should not contain double quotes. Use single
                          quotes instead.
                        </div>
                      </>
                    }
                    arrow
                  >
                    <IconButton size="small">
                      <InfoIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
              </InputLabel>
              <RadioGroup
                row
                value={templateState?.mediaType}
                onChange={handleMediaSelectionChange}
              >
                {mediaTypes
                  ?.filter((cat: any) =>
                    templateState.category !== 1
                      ? cat.value !== 6
                        ? cat
                        : false
                      : cat
                  )
                  ?.map((option) => (
                    <FormControlLabel
                      key={option?.value}
                      value={option?.value}
                      control={
                        <Radio
                          sx={{
                            color: "gray",
                            "&.Mui-checked": {
                              color: "#4CAF50",
                            },
                          }}
                        />
                      }
                      label={
                        <Typography sx={{ fontSize: 12 }}>
                          {option.label}
                        </Typography>
                      }
                      disabled={
                        canEdit === true &&
                        templateData?.mediaType >= 3 &&
                        canEdit === true &&
                        templateData?.mediaType <= 5 &&
                        (option?.value === 1 || option?.value === 2)
                      }
                    />
                  ))}
              </RadioGroup>
              {templateState?.mediaType === 2 && (
                <>
                  <TextFieldWithBorderComponent
                    inputRef={headerRef}
                    variant="outlined"
                    type="text"
                    size="small"
                    name="header"
                    value={templateState?.header}
                    onChange={handleChange}
                    fullWidth
                    label="Enter header text"
                    placeholder=""
                    sx={{
                      "& input": {
                        // fontWeight: "600",
                      },
                    }}
                    error={!!formErrors?.header}
                    helperText={formErrors?.header}
                  />
                  <Box
                    sx={{
                      display: "flex",
                      marginTop: "10px",
                      gap: 2,
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{
                        visibility:
                          headerVariablesCount >= 1 ? "hidden" : "visible",
                        // width: 200,
                        textAlign: "center",
                      }}
                      className={`${classes.variable} ${
                        selectedVariable !== "addVariable" &&
                        selectedVariable !== ""
                          ? classes.disable
                          : ""
                      }`}
                      onClick={() => {
                        if (headerVariablesCount < 1) {
                          handleAddVariable("header");
                          setSelectedVariable("addVariable");
                        }
                      }}
                    >
                      + Variables
                    </Typography>
                    <FormControl
                      size="small"
                      sx={{
                        visibility:
                          headerVariablesCount >= 1 ? "hidden" : "visible",
                      }}
                      className={`${classes.variable} ${
                        selectedVariable !== "leadratVariable" &&
                        selectedVariable !== ""
                          ? classes.disable
                          : ""
                      }`}
                    >
                      <Select
                        onChange={(event) =>
                          handleEditorAddHeaderPrebuildVariable(event)
                        }
                        value=""
                        displayEmpty
                        sx={{
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#e0e0e0",
                          },
                          "&:hover .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#bdbdbd",
                          },
                          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#e0e0e0",
                          },
                        }}
                      >
                        <MenuItem value="" disabled>
                          Leadrat Variable
                        </MenuItem>
                        {predefinedVariables.map((variable) => (
                          <MenuItem
                            key={variable}
                            value={variable}
                            sx={{
                              fontSize: "0.875rem",
                              "&:hover": {
                                backgroundColor: "rgba(76, 175, 80, 0.08)",
                              },
                              "&.Mui-selected": {
                                backgroundColor: "rgba(76, 175, 80, 0.12)",
                                "&:hover": {
                                  backgroundColor: "rgba(76, 175, 80, 0.16)",
                                },
                              },
                            }}
                          >
                            {variable}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                </>
              )}
              {(templateState?.mediaType === 3 ||
                templateState?.mediaType === 4 ||
                templateState?.mediaType === 5) && (
                <>
                  <FileUpload
                    fileType={templateState?.mediaType}
                    selectedFiles={templateState?.mediaFile}
                    handleMediaChange={handleHeaderMediaChange}
                    isFileValid={true}
                  />
                  {formErrors?.mediaFile && (
                    <FormHelperText style={{ color: "red" }}>
                      {formErrors?.mediaFile}
                    </FormHelperText>
                  )}
                </>
              )}
            </Box>
            <InputLabel
              className={classes.blackColor}
              sx={{ fontSize: { xs: 12, md: 14 }, mb: 1 }}
            >
              Body*
              <Tooltip
                title={
                  <>
                    <div>
                      1. Minimum one letter or word must be present in between
                      any two variables.
                    </div>
                    <div>
                      2. Body cannot be started or ended with a variable.
                    </div>
                    <div>
                      3. Total variables count should be less than half of the
                      words in the body.
                    </div>
                    <div>4.Body must not exceed 1024 characters.</div>
                  </>
                }
                arrow
              >
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </InputLabel>
            <FormControl fullWidth error={!!formErrors.body}>
              <div style={{ position: "relative" }}>
                <EmojiPopover
                  open={emojiPopoverOpen}
                  anchorEl={anchorEl}
                  onClose={handleCloseEmojiPopover}
                  onEmojiSelect={handleEmojiSelect}
                />
                <Typography
                  variant="body2"
                  sx={{
                    position: "absolute",
                    color:
                      templateState?.body?.length > maxLength
                        ? "error.main"
                        : "inherit",
                    right: 8,
                    top: 8,
                  }}
                >
                  {templateState?.body?.length} / {maxLength}
                </Typography>
                <DraftEditorComponent
                  selectedVariable={selectedVariable}
                  setSelectedVariable={setSelectedVariable}
                  predefinedVariables={predefinedVariables}
                  editorState={editorState}
                  setEditorState={setEditorState}
                  handleEditorStateChange={handleEditorDelayedStateChange}
                  handleSaveInboxSettings={handleSaveInboxSettings}
                  reactDraftWysiwygToolbarOptionsarticle={
                    reactDraftWysiwygToolbarOptionsarticle
                  }
                  blockRendererFn={blockRendererFn}
                  handleAddVariable={handleEditorAddVariable}
                  handleEditorAddPrebuildVariable={
                    handleEditorAddPrebuildVariable
                  }
                  file={file}
                  bodyRef={bodyRef}
                  borderColor={formErrors.body ? "red" : "rgb(205, 205, 205)"}
                />
                {formErrors.body && (
                  <FormHelperText>{formErrors.body}</FormHelperText>
                )}
              </div>
            </FormControl>
            {templateState?.mediaType === 6 && (
              <Box sx={{ marginTop: 2 }}>
                <Box>
                  <InputLabel
                    className={classes.blackColor}
                    sx={{ fontSize: { xs: 12, md: 14 }, mb: 1 }}
                  >
                    Carousel
                    <Tooltip
                      title={
                        <div>
                          Create buttons that let customers respond to your
                          message or take action.
                        </div>
                      }
                      arrow
                    >
                      <IconButton size="small">
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </InputLabel>
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    border: "2px dotted lightgrey",
                    padding: 2,
                    marginY: 2,
                  }}
                >
                  <Typography variant="body2" fontWeight={"bold"}>
                    The total number of carousel cards cannot exceed 10.
                  </Typography>
                  <Typography variant="body2">
                    {templateState?.carouselCards?.length}/10
                  </Typography>
                </Box>
                {formErrors.carouselCardsValid && (
                  <FormHelperText sx={{ color: "#d32f2f", fontWeight: "500" }}>
                    {formErrors.carouselCardsValid}
                  </FormHelperText>
                )}
                {templateState?.carouselCards?.map(
                  (carousel: any, index: number) => (
                    <Carousel
                      key={index}
                      currentCarousel={carousel}
                      carouselIndex={index}
                      carouselId={carousel.id}
                      templateState={templateState}
                      setTemplateState={setTemplateState}
                      predefinedVariables={predefinedVariables}
                      selectedVariable={selectedVariable}
                      setSelectedVariable={setSelectedVariable}
                      formErrors={formErrors}
                      setFormErrors={setFormErrors}
                      handleRemoveCarousel={handleRemoveCarousel}
                      handleCarouselFileChange={handleCarouselFileChange}
                      selectStyles={selectStyles}
                      menuItemStyles={menuItemStyles}
                      muiSelectNoBlue={muiSelectNoBlue}
                    />
                  )
                )}
              </Box>
            )}
            {templateState?.mediaType === 6 &&
              templateState?.carouselCards?.length &&
              templateState?.carouselCards?.length < 10 && (
                <Box>
                  <Button
                    onClick={handleAddMoreCarousel}
                    sx={{
                      color: "#00934F",
                      fontWeight: 700,
                      textTransform: "none",
                    }}
                    variant="text"
                  >
                    + Add more Carousel
                  </Button>
                </Box>
              )}
            {+templateState?.mediaType !== 6 && (
              <Box mt={{ xs: 1, md: 2 }} mb={2}>
                <InputLabel
                  className={classes.blackColor}
                  sx={{ fontSize: { xs: 12, md: 14 }, mb: 1 }}
                >
                  Footer
                  <Tooltip
                    title={
                      <div>Footer text must not exceed 60 characters.</div>
                    }
                    arrow
                  >
                    <IconButton size="small">
                      <InfoIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </InputLabel>
                <TextFieldWithBorderComponent
                  size="small"
                  fullWidth
                  name="footer"
                  placeholder=""
                  label="Enter footer message"
                  value={templateState?.footer || ""}
                  onChange={handleChange}
                  sx={{
                    "& input": {
                      // fontWeight: "600",
                    },
                  }}
                  error={!!formErrors?.footer}
                  helperText={formErrors?.footer}
                />
              </Box>
            )}

            {templateState?.mediaType !== 6 &&
              (templateState?.buttons?.length > 0 ? (
                <>
                  {templateState?.buttons?.map((item, index) => (
                    <Box key={index}>
                      <ActionButtonsComponent
                        formErrors={formErrors}
                        // setFormErrors={setFormErrors}
                        buttonType={item?.buttonType}
                        buttonTypesList={buttonsArray}
                        buttonValue={item?.buttonValue}
                        countryCode={item?.countryCode}
                        buttonName={item?.buttonName}
                        index={index}
                        phoneNumberCount={phoneNumberButtonCount}
                        urlsCount={urlButtonCount}
                        repliesCount={replyButtonCount}
                        onRemoveButton={() => removeButton(index)}
                        updateButton={(updatedButton: ButtonType) =>
                          updateButton(index, updatedButton)
                        }
                        handleAddVariable={handleAddVariable}
                        urlButtonVariablesCount={urlButtonVariablesCount}
                        urlButtonRef={urlButtonRef}
                        setTemplateState={setTemplateState}
                        templateState={templateState}
                        isUrlDynamic={true}
                        selectStyles={selectStyles}
                        menuItemStyles={menuItemStyles}
                        muiSelectNoBlue={muiSelectNoBlue}
                      />
                    </Box>
                  ))}
                  <Typography
                    onClick={addButton}
                    sx={{
                      fontSize: 12,
                      cursor: "pointer",
                      color: bgColors?.green,
                      textTransform: "none",
                      width: 85,
                    }}
                  >
                    + Add Button
                  </Typography>
                </>
              ) : (
                <Box width="50%" mr={1}>
                  <InputLabel
                    className={classes.blackColor}
                    sx={{ fontSize: { xs: 12, md: 14 }, mb: 2 }}
                  >
                    Buttons
                    <Tooltip
                      title="Add buttons in this order: 1. Phone number 2. Website links 3. Quick replies"
                      arrow
                    >
                      <IconButton size="small">
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </InputLabel>
                  <style>{muiSelectNoBlue}</style>
                  <FormControl fullWidth size="small">
                    <InputLabel
                      id="demo-simple-select-label"
                      sx={{ fontSize: { xs: 12, md: 14 } }}
                    >
                      Select Button
                    </InputLabel>
                    <Select
                      labelId="demo-simple-select-label"
                      id="demo-simple-select"
                      value={templateState?.buttons}
                      name="buttons"
                      label="Select Button"
                      inputProps={{ style: { fontSize: 14 } }}
                      onChange={handleChange}
                      sx={selectStyles}
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            "& .MuiMenuItem-root": {
                              menuItemStyles,
                            },
                          },
                        },
                      }}
                    >
                      {buttonsArray?.map((item, index) => (
                        <MenuItem
                          value={item?.value}
                          key={index}
                          sx={menuItemStyles}
                        >
                          {item?.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
              ))}
          </Box>
        )}
      </Grid>
      <Grid
        item
        xs={12}
        sm={12}
        md={12}
        lg={4}
        height="450px"
        sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}
      >
        <DevicePreviewComponent
          // canEdit={canEdit ? canEdit : false}
          header={templateState?.header}
          body={templateState?.body}
          footer={templateState?.footer}
          mediaType={templateState?.mediaType}
          mediaFile={templateState?.mediaFile ? templateState?.mediaFile : null}
          buttons={templateState?.buttons}
          style={{}}
          carouselCards={templateState?.carouselCards}
          currentIndex={currentIndex}
          setCurrentIndex={setCurrentIndex}
        />
      </Grid>
    </Grid>
  );
};

export default TemplateDialogContent;
