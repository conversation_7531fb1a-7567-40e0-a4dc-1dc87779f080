import React, { useState, useEffect } from "react";
import {
  Pop<PERSON>,
  Box,
  Typography,
  TextField,
  Button,
  IconButton,
  Divider,
  Chip,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { bgColors } from "../../utils/bgColors";
import CloseSvg from "../../assets/svgs/CloseSvg";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import EventIcon from "@mui/icons-material/Event";
import ClearIcon from "@mui/icons-material/Clear";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";

const useStyles = makeStyles({
  popoverContainer: {
    padding: "16px",
    minWidth: "320px",
    maxWidth: "400px",
    backgroundColor: "#fff",
  },
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "12px",
  },
  iconContainer: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
  },
  title: {
    fontWeight: "600 !important",
    fontSize: "14px !important",
    color: bgColors.black1,
  },
  closeButton: {
    color: bgColors.black1,
    padding: "4px !important",
    "&:hover": {
      backgroundColor: "rgba(0, 0, 0, 0.04)",
    },
  },
  divider: {
    margin: "12px 0 !important",
    backgroundColor: "#f0f0f0 !important",
  },
  quickDateContainer: {
    display: "flex",
    flexWrap: "wrap",
    gap: "6px",
    marginBottom: "12px",
  },
  quickDateChip: {
    fontSize: "11px !important",
    height: "24px !important",
    backgroundColor: "#f5f5f5 !important",
    color: bgColors.black1,
    border: "1px solid #e0e0e0 !important",
    "&:hover": {
      backgroundColor: "#e8f5e8 !important",
      borderColor: bgColors.green + " !important",
    },
    "&.selected": {
      backgroundColor: bgColors.green + " !important",
      color: "#fff !important",
      borderColor: bgColors.green + " !important",
    },
  },
  dateFieldContainerFrom: {
    marginBottom: "12px",
  },
  dateFieldContainer: {
    marginBottom: "16px",
  },
  columnSelectContainer: {
    marginBottom: "16px",
  },
  modernDatePicker: {
    "& .MuiOutlinedInput-root": {
      borderRadius: "8px",
      fontSize: "13px",
      "& fieldset": {
        borderColor: "#e0e0e0",
      },
      "&:hover fieldset": {
        borderColor: bgColors.green,
      },
      "&.Mui-focused fieldset": {
        borderColor: bgColors.green,
        borderWidth: "1px",
      },
    },
    "& .MuiInputLabel-root": {
      fontSize: "13px",
      "&.Mui-focused": {
        color: bgColors.green,
      },
    },
  },
  buttonContainer: {
    display: "flex",
    justifyContent: "space-between",
    gap: "8px",
    marginTop: "16px",
  },
  clearButton: {
    color: bgColors.gray1 + " !important",
    borderColor: "#e0e0e0 !important",
    fontSize: "12px !important",
    padding: "6px 16px !important",
    // textTransform: "none !important",
    borderRadius: "6px !important",
    "&:hover": {
      backgroundColor: "#f5f5f5 !important",
      borderColor: "#d0d0d0 !important",
    },
  },
  applyButton: {
    backgroundColor: bgColors.green + " !important",
    color: "#fff !important",
    fontSize: "12px !important",
    padding: "6px 16px !important",
    // textTransform: "none !important",
    borderRadius: "6px !important",
    "&:hover": {
      backgroundColor: "#2e7d32 !important",
    },
    "&:disabled": {
      backgroundColor: "#e0e0e0 !important",
      color: "#999 !important",
    },
  },
  datePickerPopper: {
    zIndex: 1400,
  },
});

export interface DateRange {
  startDate: string;
  endDate: string;
}

export interface DateColumnRange extends DateRange {
  column: string;
}

export interface DateColumnRangeFilterPopoverProps {
  anchorEl: HTMLElement | null;
  handleClose: () => void;
  onApplyFilter: (dateRange: DateColumnRange) => void;
  initialDateRange?: DateColumnRange;
  title?: string;
  columnOptions?: { value: string; label: string }[];
}

const quickDateOptions = [
  { label: "Today", value: "today" },
  { label: "Yesterday", value: "yesterday" },
  { label: "Last 7 days", value: "last7days" },
  { label: "Last 30 days", value: "last30days" },
  { label: "This month", value: "thisMonth" },
  { label: "Last month", value: "lastMonth" },
];

const defaultColumnOptions = [
  { value: "CreatedDate", label: "Created Date" },
  { value: "LastMessageAt", label: "Last Message At" },
];

const DateColumnRangeFilterPopover: React.FC<
  DateColumnRangeFilterPopoverProps
> = ({
  anchorEl,
  handleClose,
  onApplyFilter,
  initialDateRange,
  title = "Date Range Filter",
  columnOptions = defaultColumnOptions,
}) => {
  const classes = useStyles();
  const [dateRange, setDateRange] = useState<DateColumnRange>({
    startDate: initialDateRange?.startDate || "",
    endDate: initialDateRange?.endDate || "",
    column:
      initialDateRange?.column || columnOptions[0]?.value || "CreatedDate",
  });
  const [selectedQuickDate, setSelectedQuickDate] = useState<string>("");

  const getCurrentDate = () => {
    return dayjs().format("YYYY-MM-DD");
  };

  const formatDateForDisplay = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const handleStartDateChange = (date: Dayjs | null) => {
    const newStartDate = date ? date.format("YYYY-MM-DD") : "";
    setDateRange((prev) => ({
      ...prev,
      startDate: newStartDate,
    }));

    // Reset end date if start date is after current end date
    if (newStartDate && dateRange.endDate && newStartDate > dateRange.endDate) {
      setDateRange((prev) => ({
        ...prev,
        endDate: "",
      }));
    }

    // Check if the new date range matches any quick select option
    checkAndSetQuickSelect(newStartDate, dateRange.endDate);
  };

  const handleEndDateChange = (date: Dayjs | null) => {
    const newEndDate = date ? date.format("YYYY-MM-DD") : "";
    setDateRange((prev) => ({
      ...prev,
      endDate: newEndDate,
    }));

    // Check if the new date range matches any quick select option
    checkAndSetQuickSelect(dateRange.startDate, newEndDate);
  };

  const handleColumnChange = (event: any) => {
    setDateRange((prev) => ({
      ...prev,
      column: event.target.value,
    }));
  };

  const checkAndSetQuickSelect = (startDate: string, endDate: string) => {
    const today = dayjs();

    // Check if dates match any quick select option
    for (const option of quickDateOptions) {
      const { startDate: optionStart, endDate: optionEnd } = getQuickDateRange(
        option.value
      );
      if (startDate === optionStart && endDate === optionEnd) {
        setSelectedQuickDate(option.value);
        return;
      }
    }

    setSelectedQuickDate("");
  };

  const getQuickDateRange = (quickDateValue: string) => {
    const today = dayjs();
    let startDate = today.format("YYYY-MM-DD");
    let endDate = today.format("YYYY-MM-DD");

    switch (quickDateValue) {
      case "today":
        startDate = today.format("YYYY-MM-DD");
        endDate = startDate;
        break;
      case "yesterday":
        startDate = today.subtract(1, "day").format("YYYY-MM-DD");
        endDate = startDate;
        break;
      case "last7days":
        startDate = today.subtract(7, "day").format("YYYY-MM-DD");
        break;
      case "last30days":
        startDate = today.subtract(30, "day").format("YYYY-MM-DD");
        break;
      case "thisMonth":
        startDate = today.startOf("month").format("YYYY-MM-DD");
        break;
      case "lastMonth":
        startDate = today
          .subtract(1, "month")
          .startOf("month")
          .format("YYYY-MM-DD");
        endDate = today
          .subtract(1, "month")
          .endOf("month")
          .format("YYYY-MM-DD");
        break;
    }

    return { startDate, endDate };
  };

  const handleQuickDateSelect = (quickDateValue: string) => {
    const { startDate, endDate } = getQuickDateRange(quickDateValue);
    setDateRange((prev) => ({ ...prev, startDate, endDate }));
    setSelectedQuickDate(quickDateValue);
  };

  const handleClear = () => {
    setDateRange({
      startDate: "",
      endDate: "",
      column: columnOptions[0]?.value || "CreatedDate",
    });
    setSelectedQuickDate("");
  };

  const handleApply = () => {
    if (dateRange.startDate && dateRange.endDate && dateRange.column) {
      onApplyFilter(dateRange);
    }
  };

  const handleCloseWithReset = () => {
    // Reset to initial values when closing without applying
    setDateRange({
      startDate: initialDateRange?.startDate || "",
      endDate: initialDateRange?.endDate || "",
      column:
        initialDateRange?.column || columnOptions[0]?.value || "CreatedDate",
    });
    setSelectedQuickDate("");
    handleClose();
  };

  const isApplyDisabled =
    !dateRange.startDate || !dateRange.endDate || !dateRange.column;

  return (
    <Popover
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={handleCloseWithReset}
      anchorOrigin={{
        vertical: "top",
        horizontal: "right",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      sx={{
        zIndex: 1300,
      }}
      PaperProps={{
        sx: {
          marginLeft: 1,
        },
      }}
    >
      <Paper elevation={3} className={classes.popoverContainer}>
        <Box className={classes.headerContainer}>
          <Box className={classes.iconContainer}>
            <EventIcon fontSize="small" />
            <Typography className={classes.title}>{title}</Typography>
          </Box>
          <IconButton
            onClick={handleCloseWithReset}
            className={classes.closeButton}
            size="small"
          >
            <CloseSvg />
          </IconButton>
        </Box>

        <Divider className={classes.divider} />

        {/* Column Selection */}
        <Box className={classes.columnSelectContainer}>
          <FormControl fullWidth size="small">
            <InputLabel>Date Column</InputLabel>
            <Select
              value={dateRange.column}
              onChange={handleColumnChange}
              label="Date Column"
              className={classes.modernDatePicker}
            >
              {columnOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        {/* Quick Date Options */}
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ mb: 1, display: "block", fontSize: "11px" }}
        >
          Quick Select
        </Typography>
        <Box className={classes.quickDateContainer}>
          {quickDateOptions.map((option) => (
            <Chip
              key={option.value}
              label={option.label}
              className={`${classes.quickDateChip} ${
                selectedQuickDate === option.value ? "selected" : ""
              }`}
              onClick={() => handleQuickDateSelect(option.value)}
              size="small"
            />
          ))}
        </Box>

        <Divider className={classes.divider} />

        {/* Custom Date Range */}
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ mb: 1, display: "block", fontSize: "11px" }}
        >
          Custom Range
        </Typography>

        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <Box className={classes.dateFieldContainerFrom}>
            <DatePicker
              label="From Date"
              value={dateRange.startDate ? dayjs(dateRange.startDate) : null}
              onChange={handleStartDateChange}
              maxDate={dayjs()}
              format="DD/MM/YYYY"
              slotProps={{
                textField: {
                  className: classes.modernDatePicker,
                  fullWidth: true,
                  size: "small",
                  InputLabelProps: {
                    shrink: true,
                  },
                },
                popper: {
                  className: classes.datePickerPopper,
                },
              }}
            />
          </Box>

          <Box className={classes.dateFieldContainer}>
            <DatePicker
              label="To Date"
              value={dateRange.endDate ? dayjs(dateRange.endDate) : null}
              onChange={handleEndDateChange}
              minDate={
                dateRange.startDate ? dayjs(dateRange.startDate) : undefined
              }
              maxDate={dayjs()}
              format="DD/MM/YYYY"
              disabled={!dateRange.startDate}
              slotProps={{
                textField: {
                  className: classes.modernDatePicker,
                  fullWidth: true,
                  size: "small",
                  InputLabelProps: {
                    shrink: true,
                  },
                },
                popper: {
                  className: classes.datePickerPopper,
                },
              }}
            />
          </Box>
        </LocalizationProvider>

        {/* Action Buttons */}
        <Box className={classes.buttonContainer}>
          <Button
            variant="outlined"
            onClick={handleClear}
            className={classes.clearButton}
            startIcon={<ClearIcon fontSize="small" />}
          >
            Clear
          </Button>
          <Button
            variant="contained"
            onClick={handleApply}
            disabled={isApplyDisabled}
            className={classes.applyButton}
            startIcon={<CalendarTodayIcon fontSize="small" />}
          >
            Apply Filter
          </Button>
        </Box>
      </Paper>
    </Popover>
  );
};

export default DateColumnRangeFilterPopover;
